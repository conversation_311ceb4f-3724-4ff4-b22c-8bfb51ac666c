[{"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/contact/page.tsx": "1", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/page.tsx": "2", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/products/page.tsx": "3", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/contact/route.ts": "4", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/robots/route.ts": "5", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/sitemap/route.ts": "6", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/en/page.tsx": "7", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx": "8", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/es/page.tsx": "9", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/ja/page.tsx": "10", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx": "11", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx": "12", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/page.tsx": "13", "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/zh-CN/page.tsx": "14", "/Users/<USER>/Vibe-Code/test-web-1.0/src/components/forms/ContactForm.tsx": "15", "/Users/<USER>/Vibe-Code/test-web-1.0/src/components/ui/LanguageSwitcher.tsx": "16", "/Users/<USER>/Vibe-Code/test-web-1.0/src/i18n.ts": "17", "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/content.ts": "18", "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/email.ts": "19", "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/i18n.ts": "20", "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/seo.ts": "21", "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/test-content.ts": "22", "/Users/<USER>/Vibe-Code/test-web-1.0/src/types/contact.ts": "23", "/Users/<USER>/Vibe-Code/test-web-1.0/src/types/content.ts": "24", "/Users/<USER>/Vibe-Code/test-web-1.0/src/components/ui/LanguageSwitchFeedback.tsx": "25", "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/language-preference.ts": "26"}, {"size": 8993, "mtime": 1749924814565, "results": "27", "hashOfConfig": "28"}, {"size": 804, "mtime": 1749919231118, "results": "29", "hashOfConfig": "28"}, {"size": 7034, "mtime": 1749923649743, "results": "30", "hashOfConfig": "28"}, {"size": 5994, "mtime": 1749924914139, "results": "31", "hashOfConfig": "28"}, {"size": 701, "mtime": 1749921426715, "results": "32", "hashOfConfig": "28"}, {"size": 3939, "mtime": 1749921417016, "results": "33", "hashOfConfig": "28"}, {"size": 751, "mtime": 1749919419831, "results": "34", "hashOfConfig": "28"}, {"size": 7041, "mtime": 1749924826921, "results": "35", "hashOfConfig": "28"}, {"size": 763, "mtime": 1749919464198, "results": "36", "hashOfConfig": "28"}, {"size": 764, "mtime": 1749919447062, "results": "37", "hashOfConfig": "28"}, {"size": 7002, "mtime": 1749918189874, "results": "38", "hashOfConfig": "28"}, {"size": 6011, "mtime": 1749924851242, "results": "39", "hashOfConfig": "28"}, {"size": 134, "mtime": 1749919604910, "results": "40", "hashOfConfig": "28"}, {"size": 754, "mtime": 1749919262388, "results": "41", "hashOfConfig": "28"}, {"size": 11665, "mtime": 1749922875885, "results": "42", "hashOfConfig": "28"}, {"size": 11083, "mtime": 1749924948559, "results": "43", "hashOfConfig": "28"}, {"size": 2598, "mtime": 1749924985549, "results": "44", "hashOfConfig": "28"}, {"size": 1337, "mtime": 1749915822348, "results": "45", "hashOfConfig": "28"}, {"size": 9993, "mtime": 1749921640963, "results": "46", "hashOfConfig": "28"}, {"size": 7392, "mtime": 1749925024762, "results": "47", "hashOfConfig": "28"}, {"size": 8360, "mtime": 1749921392362, "results": "48", "hashOfConfig": "28"}, {"size": 1425, "mtime": 1749915620427, "results": "49", "hashOfConfig": "28"}, {"size": 1487, "mtime": 1749921112907, "results": "50", "hashOfConfig": "28"}, {"size": 4999, "mtime": 1749915348407, "results": "51", "hashOfConfig": "28"}, {"size": 3051, "mtime": 1749924327991, "results": "52", "hashOfConfig": "28"}, {"size": 3809, "mtime": 1749924306260, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "m5zqkg", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/contact/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/products/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/contact/route.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/robots/route.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/sitemap/route.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/en/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/es/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/ja/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/app/zh-CN/page.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/components/forms/ContactForm.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/components/ui/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/i18n.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/content.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/email.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/i18n.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/seo.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/test-content.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/types/contact.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/types/content.ts", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/components/ui/LanguageSwitchFeedback.tsx", [], [], "/Users/<USER>/Vibe-Code/test-web-1.0/src/lib/language-preference.ts", [], []]