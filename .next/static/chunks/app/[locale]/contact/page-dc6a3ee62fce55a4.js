(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[961],{2713:function(e,s,r){Promise.resolve().then(r.bind(r,4461))},4461:function(e,s,r){"use strict";r.d(s,{default:function(){return m}});var l=r(7501),a=r(4288),t=r(2429),n=r(7977),c=r(4844),o=r(1543),d=r(4602);let i=c.z.object({name:c.z.string().min(2,"Name must be at least 2 characters"),email:c.z.string().email("Please enter a valid email address"),company:c.z.string().optional(),phone:c.z.string().optional(),subject:c.z.string().min(5,"Subject must be at least 5 characters"),message:c.z.string().min(10,"Message must be at least 10 characters"),productInterest:c.z.array(c.z.string()).optional(),preferredContact:c.z.enum(["email","phone"]),urgency:c.z.enum(["low","medium","high"]),consent:c.z.boolean().refine(e=>!0===e,"You must agree to the privacy policy")});function m(e){let{className:s="",onSuccess:r}=e,c=(0,o.T_)("contact.form"),m=(0,d.bU)(),[u,x]=(0,a.useState)(!1),[b,h]=(0,a.useState)("idle"),[p,g]=(0,a.useState)(""),{register:f,handleSubmit:y,formState:{errors:j},reset:N,watch:v}=(0,t.cI)({resolver:(0,n.F)(i),defaultValues:{preferredContact:"email",urgency:"medium",productInterest:[]}}),w=v("preferredContact"),k=async e=>{x(!0),h("idle"),g("");try{let s=await fetch("/api/contact",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,locale:m||"en"})}),l=await s.json();l.success?(h("success"),N(),null==r||r()):(h("error"),g(l.error||"An error occurred while sending your message"))}catch(e){h("error"),g("Network error. Please try again.")}finally{x(!1)}},z=(0,o.T_)("products.interests"),P=(0,o.T_)("products.services"),C=[{value:"flood-barriers",label:z("floodBarriers")},{value:"water-pumps",label:z("waterPumps")},{value:"drainage-systems",label:z("drainageSystems")},{value:"emergency-equipment",label:z("emergencyEquipment")},{value:"consulting-services",label:P("consulting")},{value:"maintenance-support",label:P("maintenance")}];return(0,l.jsx)("div",{className:"max-w-2xl mx-auto ".concat(s),children:(0,l.jsxs)("form",{onSubmit:y(k),className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:[c("name")," *"]}),(0,l.jsx)("input",{...f("name"),type:"text",id:"name",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:c("namePlaceholder")}),j.name&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:j.name.message})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:[c("email")," *"]}),(0,l.jsx)("input",{...f("email"),type:"email",id:"email",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:c("emailPlaceholder")}),j.email&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:j.email.message})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"company",className:"block text-sm font-medium text-gray-700 mb-2",children:c("company")}),(0,l.jsx)("input",{...f("company"),type:"text",id:"company",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:c("companyPlaceholder")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:c("phone")}),(0,l.jsx)("input",{...f("phone"),type:"tel",id:"phone",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:c("phonePlaceholder")})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:[c("subject")," *"]}),(0,l.jsx)("input",{...f("subject"),type:"text",id:"subject",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:c("subjectPlaceholder")}),j.subject&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:j.subject.message})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:[c("message")," *"]}),(0,l.jsx)("textarea",{...f("message"),id:"message",rows:5,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:c("messagePlaceholder")}),j.message&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:j.message.message})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:c("productInterest")}),(0,l.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:C.map(e=>(0,l.jsxs)("label",{className:"flex items-center",children:[(0,l.jsx)("input",{...f("productInterest"),type:"checkbox",value:e.value,className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,l.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:e.label})]},e.value))})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:[c("preferredContact")," *"]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("label",{className:"flex items-center",children:[(0,l.jsx)("input",{...f("preferredContact"),type:"radio",value:"email",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,l.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:c("email")})]}),(0,l.jsxs)("label",{className:"flex items-center",children:[(0,l.jsx)("input",{...f("preferredContact"),type:"radio",value:"phone",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,l.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:c("phone")})]})]}),"phone"===w&&!v("phone")&&(0,l.jsx)("p",{className:"mt-1 text-sm text-amber-600",children:c("phoneRequired")})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{htmlFor:"urgency",className:"block text-sm font-medium text-gray-700 mb-2",children:[c("urgency")," *"]}),(0,l.jsxs)("select",{...f("urgency"),id:"urgency",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,l.jsx)("option",{value:"low",children:c("urgencyLow")}),(0,l.jsx)("option",{value:"medium",children:c("urgencyMedium")}),(0,l.jsx)("option",{value:"high",children:c("urgencyHigh")})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"flex items-start",children:[(0,l.jsx)("input",{...f("consent"),type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"}),(0,l.jsxs)("span",{className:"ml-2 text-sm text-gray-700",children:[c("consent")," ",(0,l.jsx)("a",{href:"/privacy",className:"text-blue-600 hover:text-blue-800 underline",children:c("privacyPolicy")})]})]}),j.consent&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:j.consent.message})]}),"success"===b&&(0,l.jsx)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-md",children:(0,l.jsx)("p",{className:"text-green-800",children:c("successMessage")})}),"error"===b&&(0,l.jsx)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-md",children:(0,l.jsx)("p",{className:"text-red-800",children:p||c("errorMessage")})}),(0,l.jsx)("div",{children:(0,l.jsx)("button",{type:"submit",disabled:u,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:c(u?"sending":"submit")})})]})})}}},function(e){e.O(0,[602,357,15,871,744],function(){return e(e.s=2713)}),_N_E=e.O()}]);