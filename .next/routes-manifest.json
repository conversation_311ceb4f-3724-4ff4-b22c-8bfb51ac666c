{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}, {"source": "/home", "destination": "/", "statusCode": 308, "regex": "^(?!/_next)/home(?:/)?$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}], "regex": "^(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/[locale]", "regex": "^/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)(?:/)?$"}, {"page": "/[locale]/contact", "regex": "^/([^/]+?)/contact(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/contact(?:/)?$"}, {"page": "/[locale]/products", "regex": "^/([^/]+?)/products(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/products(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/en", "regex": "^/en(?:/)?$", "routeKeys": {}, "namedRegex": "^/en(?:/)?$"}, {"page": "/es", "regex": "^/es(?:/)?$", "routeKeys": {}, "namedRegex": "^/es(?:/)?$"}, {"page": "/ja", "regex": "^/ja(?:/)?$", "routeKeys": {}, "namedRegex": "^/ja(?:/)?$"}, {"page": "/zh-CN", "regex": "^/zh\\-CN(?:/)?$", "routeKeys": {}, "namedRegex": "^/zh\\-CN(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}