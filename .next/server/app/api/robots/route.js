"use strict";(()=>{var e={};e.id=45,e.ids=[45],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},2499:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>m,patchFetch:()=>f,requestAsyncStorage:()=>c,routeModule:()=>u,serverHooks:()=>g,staticGenerationAsyncStorage:()=>d});var o={};r.r(o),r.d(o,{GET:()=>p});var n=r(8738),a=r(3163),i=r(9803),s=r(5950),l=r(2665);async function p(){try{let e=(0,l.xU)();return new s.NextResponse(e,{status:200,headers:{"Content-Type":"text/plain","Cache-Control":"public, max-age=86400, s-maxage=86400"}})}catch(e){return new s.NextResponse("User-agent: *\nDisallow: /",{status:500,headers:{"Content-Type":"text/plain"}})}}let u=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/robots/route",pathname:"/api/robots",filename:"route",bundlePath:"app/api/robots/route"},resolvedPagePath:"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/api/robots/route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:c,staticGenerationAsyncStorage:d,serverHooks:g}=u,m="/api/robots/route";function f(){return(0,i.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:d})}},2665:(e,t,r)=>{r.d(t,{_G:()=>n,ep:()=>o,xU:()=>a});let o={name:"Tucsenberg",title:"Tucsenberg - Professional Flood Protection Equipment Manufacturer",description:"Tucsenberg specializes in flood protection equipment R&D, design and manufacturing, providing high-quality flood protection products and comprehensive solutions for global users.",url:process.env.NEXT_PUBLIC_SITE_URL||"https://tucsenberg.com",ogImage:"/images/og-image.jpg",twitterHandle:"@tucsenberg",keywords:["flood protection","flood barriers","flood control","emergency flood protection","industrial flood protection","water pumps","drainage systems","emergency equipment","Tucsenberg"]};function n(e="en"){let t=o.url,r="en"===e?"":`/${e}`;return[{url:"",priority:1,changefreq:"daily"},{url:"/about",priority:.8,changefreq:"monthly"},{url:"/contact",priority:.8,changefreq:"monthly"},{url:"/products",priority:.9,changefreq:"weekly"},{url:"/news",priority:.7,changefreq:"weekly"},{url:"/support",priority:.6,changefreq:"monthly"}].map(e=>({url:`${t}${r}${e.url}`,lastModified:new Date().toISOString(),priority:e.priority,changefreq:e.changefreq}))}function a(){let e=o.url;return`User-agent: *
Allow: /

# Sitemaps
Sitemap: ${e}/sitemap.xml

# Crawl-delay
Crawl-delay: 1

# Disallow admin and private areas
Disallow: /admin/
Disallow: /api/
Disallow: /_next/
Disallow: /private/

# Allow specific API endpoints
Allow: /api/sitemap
Allow: /api/robots
`}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[999,946],()=>r(2499));module.exports=o})();