(()=>{var e={};e.id=961,e.ids=[961],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5094:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>h,originalPathname:()=>c,pages:()=>u,routeModule:()=>m,tree:()=>o}),r(3498),r(1812),r(8002),r(1571);var a=r(6826),s=r(3163),i=r(9993),n=r.n(i),l=r(4281),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(t,d);let o=["",{children:["[locale]",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3498)),"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/contact/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,1812)),"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,8002)),"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,1571)),"/Users/<USER>/Vibe-Code/test-web-1.0/src/app/not-found.tsx"]}],u=["/Users/<USER>/Vibe-Code/test-web-1.0/src/app/[locale]/contact/page.tsx"],c="/[locale]/contact/page",h={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/[locale]/contact/page",pathname:"/[locale]/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},8001:(e,t,r)=>{Promise.resolve().then(r.bind(r,3009))},3009:(e,t,r)=>{"use strict";let a;r.d(t,{default:()=>tJ});var s,i,n,l,d=r(6788),o=r(7129),u=r.n(o),c=e=>"checkbox"===e.type,h=e=>e instanceof Date,m=e=>null==e;let p=e=>"object"==typeof e;var f=e=>!m(e)&&!Array.isArray(e)&&p(e)&&!h(e),y=e=>f(e)&&e.target?c(e.target)?e.target.checked:e.target.value:e,g=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,v=(e,t)=>e.has(g(t)),_=e=>{let t=e.constructor&&e.constructor.prototype;return f(t)&&t.hasOwnProperty("isPrototypeOf")},x="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function b(e){let t;let r=Array.isArray(e),a="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else if(!(!(x&&(e instanceof Blob||a))&&(r||f(e))))return e;else if(t=r?[]:{},r||_(e))for(let r in e)e.hasOwnProperty(r)&&(t[r]=b(e[r]));else t=e;return t}var k=e=>Array.isArray(e)?e.filter(Boolean):[],w=e=>void 0===e,j=(e,t,r)=>{if(!t||!f(e))return r;let a=k(t.split(/[,[\].]+?/)).reduce((e,t)=>m(e)?e:e[t],e);return w(a)||a===e?w(e[t])?r:e[t]:a},N=e=>"boolean"==typeof e,A=e=>/^\w*$/.test(e),S=e=>k(e.replace(/["|']|\]/g,"").split(/\.|\[/)),C=(e,t,r)=>{let a=-1,s=A(t)?[t]:S(t),i=s.length,n=i-1;for(;++a<i;){let t=s[a],i=r;if(a!==n){let r=e[t];i=f(r)||Array.isArray(r)?r:isNaN(+s[a+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=i,e=e[t]}};let O={BLUR:"blur",FOCUS_OUT:"focusout"},T={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},Z={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"};u().createContext(null);var F=(e,t,r,a=!0)=>{let s={defaultValues:t._defaultValues};for(let i in e)Object.defineProperty(s,i,{get:()=>(t._proxyFormState[i]!==T.all&&(t._proxyFormState[i]=!a||T.all),r&&(r[i]=!0),e[i])});return s};let V="undefined"!=typeof window?o.useLayoutEffect:o.useEffect;var E=e=>"string"==typeof e,P=(e,t,r,a,s)=>E(e)?(a&&t.watch.add(e),j(r,e,s)):Array.isArray(e)?e.map(e=>(a&&t.watch.add(e),j(r,e))):(a&&(t.watchAll=!0),r),D=(e,t,r,a,s)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[a]:s||!0}}:{},I=e=>Array.isArray(e)?e:[e],R=()=>{let e=[];return{get observers(){return e},next:t=>{for(let r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},M=e=>m(e)||!p(e);function $(e,t){if(M(e)||M(t))return e===t;if(h(e)&&h(t))return e.getTime()===t.getTime();let r=Object.keys(e),a=Object.keys(t);if(r.length!==a.length)return!1;for(let s of r){let r=e[s];if(!a.includes(s))return!1;if("ref"!==s){let e=t[s];if(h(r)&&h(e)||f(r)&&f(e)||Array.isArray(r)&&Array.isArray(e)?!$(r,e):r!==e)return!1}}return!0}var L=e=>f(e)&&!Object.keys(e).length,U=e=>"file"===e.type,z=e=>"function"==typeof e,q=e=>{if(!x)return!1;let t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},B=e=>"select-multiple"===e.type,W=e=>"radio"===e.type,K=e=>W(e)||c(e),G=e=>q(e)&&e.isConnected;function H(e,t){let r=Array.isArray(t)?t:A(t)?[t]:S(t),a=1===r.length?e:function(e,t){let r=t.slice(0,-1).length,a=0;for(;a<r;)e=w(e)?a++:e[t[a++]];return e}(e,r),s=r.length-1,i=r[s];return a&&delete a[i],0!==s&&(f(a)&&L(a)||Array.isArray(a)&&function(e){for(let t in e)if(e.hasOwnProperty(t)&&!w(e[t]))return!1;return!0}(a))&&H(e,r.slice(0,-1)),e}var J=e=>{for(let t in e)if(z(e[t]))return!0;return!1};function Y(e,t={}){let r=Array.isArray(e);if(f(e)||r)for(let r in e)Array.isArray(e[r])||f(e[r])&&!J(e[r])?(t[r]=Array.isArray(e[r])?[]:{},Y(e[r],t[r])):m(e[r])||(t[r]=!0);return t}var X=(e,t)=>(function e(t,r,a){let s=Array.isArray(t);if(f(t)||s)for(let s in t)Array.isArray(t[s])||f(t[s])&&!J(t[s])?w(r)||M(a[s])?a[s]=Array.isArray(t[s])?Y(t[s],[]):{...Y(t[s])}:e(t[s],m(r)?{}:r[s],a[s]):a[s]=!$(t[s],r[s]);return a})(e,t,Y(t));let Q={value:!1,isValid:!1},ee={value:!0,isValid:!0};var et=e=>{if(Array.isArray(e)){if(e.length>1){let t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!w(e[0].attributes.value)?w(e[0].value)||""===e[0].value?ee:{value:e[0].value,isValid:!0}:ee:Q}return Q},er=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:a})=>w(e)?e:t?""===e?NaN:e?+e:e:r&&E(e)?new Date(e):a?a(e):e;let ea={isValid:!1,value:null};var es=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,ea):ea;function ei(e){let t=e.ref;return U(t)?t.files:W(t)?es(e.refs).value:B(t)?[...t.selectedOptions].map(({value:e})=>e):c(t)?et(e.refs).value:er(w(t.value)?e.ref.value:t.value,e)}var en=(e,t,r,a)=>{let s={};for(let r of e){let e=j(t,r);e&&C(s,r,e._f)}return{criteriaMode:r,names:[...e],fields:s,shouldUseNativeValidation:a}},el=e=>e instanceof RegExp,ed=e=>w(e)?e:el(e)?e.source:f(e)?el(e.value)?e.value.source:e.value:e,eo=e=>({isOnSubmit:!e||e===T.onSubmit,isOnBlur:e===T.onBlur,isOnChange:e===T.onChange,isOnAll:e===T.all,isOnTouch:e===T.onTouched});let eu="AsyncFunction";var ec=e=>!!e&&!!e.validate&&!!(z(e.validate)&&e.validate.constructor.name===eu||f(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===eu)),eh=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),em=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));let ep=(e,t,r,a)=>{for(let s of r||Object.keys(e)){let r=j(e,s);if(r){let{_f:e,...i}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],s)&&!a||e.ref&&t(e.ref,e.name)&&!a)return!0;if(ep(i,t))break}else if(f(i)&&ep(i,t))break}}};function ef(e,t,r){let a=j(e,r);if(a||A(r))return{error:a,name:r};let s=r.split(".");for(;s.length;){let a=s.join("."),i=j(t,a),n=j(e,a);if(i&&!Array.isArray(i)&&r!==a)break;if(n&&n.type)return{name:a,error:n};if(n&&n.root&&n.root.type)return{name:`${a}.root`,error:n.root};s.pop()}return{name:r}}var ey=(e,t,r,a)=>{r(e);let{name:s,...i}=e;return L(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(e=>t[e]===(!a||T.all))},eg=(e,t,r)=>!e||!t||e===t||I(e).some(e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))),ev=(e,t,r,a,s)=>!s.isOnAll&&(!r&&s.isOnTouch?!(t||e):(r?a.isOnBlur:s.isOnBlur)?!e:(r?!a.isOnChange:!s.isOnChange)||e),e_=(e,t)=>!k(j(e,t)).length&&H(e,t),ex=(e,t,r)=>{let a=I(j(e,r));return C(a,"root",t[r]),C(e,r,a),e},eb=e=>E(e);function ek(e,t,r="validate"){if(eb(e)||Array.isArray(e)&&e.every(eb)||N(e)&&!e)return{type:r,message:eb(e)?e:"",ref:t}}var ew=e=>f(e)&&!el(e)?e:{value:e,message:""},ej=async(e,t,r,a,s,i)=>{let{ref:n,refs:l,required:d,maxLength:o,minLength:u,min:h,max:p,pattern:y,validate:g,name:v,valueAsNumber:_,mount:x}=e._f,b=j(r,v);if(!x||t.has(v))return{};let k=l?l[0]:n,A=e=>{s&&k.reportValidity&&(k.setCustomValidity(N(e)?"":e||""),k.reportValidity())},S={},C=W(n),O=c(n),T=(_||U(n))&&w(n.value)&&w(b)||q(n)&&""===n.value||""===b||Array.isArray(b)&&!b.length,F=D.bind(null,v,a,S),V=(e,t,r,a=Z.maxLength,s=Z.minLength)=>{let i=e?t:r;S[v]={type:e?a:s,message:i,ref:n,...F(e?a:s,i)}};if(i?!Array.isArray(b)||!b.length:d&&(!(C||O)&&(T||m(b))||N(b)&&!b||O&&!et(l).isValid||C&&!es(l).isValid)){let{value:e,message:t}=eb(d)?{value:!!d,message:d}:ew(d);if(e&&(S[v]={type:Z.required,message:t,ref:k,...F(Z.required,t)},!a))return A(t),S}if(!T&&(!m(h)||!m(p))){let e,t;let r=ew(p),s=ew(h);if(m(b)||isNaN(b)){let a=n.valueAsDate||new Date(b),i=e=>new Date(new Date().toDateString()+" "+e),l="time"==n.type,d="week"==n.type;E(r.value)&&b&&(e=l?i(b)>i(r.value):d?b>r.value:a>new Date(r.value)),E(s.value)&&b&&(t=l?i(b)<i(s.value):d?b<s.value:a<new Date(s.value))}else{let a=n.valueAsNumber||(b?+b:b);m(r.value)||(e=a>r.value),m(s.value)||(t=a<s.value)}if((e||t)&&(V(!!e,r.message,s.message,Z.max,Z.min),!a))return A(S[v].message),S}if((o||u)&&!T&&(E(b)||i&&Array.isArray(b))){let e=ew(o),t=ew(u),r=!m(e.value)&&b.length>+e.value,s=!m(t.value)&&b.length<+t.value;if((r||s)&&(V(r,e.message,t.message),!a))return A(S[v].message),S}if(y&&!T&&E(b)){let{value:e,message:t}=ew(y);if(el(e)&&!b.match(e)&&(S[v]={type:Z.pattern,message:t,ref:n,...F(Z.pattern,t)},!a))return A(t),S}if(g){if(z(g)){let e=ek(await g(b,r),k);if(e&&(S[v]={...e,...F(Z.validate,e.message)},!a))return A(e.message),S}else if(f(g)){let e={};for(let t in g){if(!L(e)&&!a)break;let s=ek(await g[t](b,r),k,t);s&&(e={...s,...F(t,s.message)},A(s.message),a&&(S[v]=e))}if(!L(e)&&(S[v]={ref:k,...e},!a))return S}}return A(!0),S};let eN={mode:T.onSubmit,reValidateMode:T.onChange,shouldFocusError:!0},eA=(e,t,r)=>{if(e&&"reportValidity"in e){let a=j(r,t);e.setCustomValidity(a&&a.message||""),e.reportValidity()}},eS=(e,t)=>{for(let r in t.fields){let a=t.fields[r];a&&a.ref&&"reportValidity"in a.ref?eA(a.ref,r,e):a.refs&&a.refs.forEach(t=>eA(t,r,e))}},eC=(e,t)=>{t.shouldUseNativeValidation&&eS(e,t);let r={};for(let a in e){let s=j(t.fields,a),i=Object.assign(e[a]||{},{ref:s&&s.ref});if(eO(t.names||Object.keys(e),a)){let e=Object.assign({},j(r,a));C(e,"root",i),C(r,a,e)}else C(r,a,i)}return r},eO=(e,t)=>e.some(e=>e.startsWith(t+"."));var eT=function(e,t){for(var r={};e.length;){var a=e[0],s=a.code,i=a.message,n=a.path.join(".");if(!r[n]){if("unionErrors"in a){var l=a.unionErrors[0].errors[0];r[n]={message:l.message,type:l.code}}else r[n]={message:i,type:s}}if("unionErrors"in a&&a.unionErrors.forEach(function(t){return t.errors.forEach(function(t){return e.push(t)})}),t){var d=r[n].types,o=d&&d[a.code];r[n]=D(n,t,r,s,o?[].concat(o,a.message):a.message)}e.shift()}return r};(function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let r of e)t[r]=r;return t},e.getValidEnumValues=t=>{let r=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),a={};for(let e of r)a[e]=t[e];return e.objectValues(a)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t},e.find=(e,t)=>{for(let r of e)if(t(r))return r},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t})(s||(s={})),(i||(i={})).mergeShapes=(e,t)=>({...e,...t});let eZ=s.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),eF=e=>{switch(typeof e){case"undefined":return eZ.undefined;case"string":return eZ.string;case"number":return Number.isNaN(e)?eZ.nan:eZ.number;case"boolean":return eZ.boolean;case"function":return eZ.function;case"bigint":return eZ.bigint;case"symbol":return eZ.symbol;case"object":if(Array.isArray(e))return eZ.array;if(null===e)return eZ.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return eZ.promise;if("undefined"!=typeof Map&&e instanceof Map)return eZ.map;if("undefined"!=typeof Set&&e instanceof Set)return eZ.set;if("undefined"!=typeof Date&&e instanceof Date)return eZ.date;return eZ.object;default:return eZ.unknown}},eV=s.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class eE extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},r={_errors:[]},a=e=>{for(let s of e.issues)if("invalid_union"===s.code)s.unionErrors.map(a);else if("invalid_return_type"===s.code)a(s.returnTypeError);else if("invalid_arguments"===s.code)a(s.argumentsError);else if(0===s.path.length)r._errors.push(t(s));else{let e=r,a=0;for(;a<s.path.length;){let r=s.path[a];a===s.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(t(s))):e[r]=e[r]||{_errors:[]},e=e[r],a++}}};return a(this),r}static assert(e){if(!(e instanceof eE))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,s.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},r=[];for(let a of this.issues)a.path.length>0?(t[a.path[0]]=t[a.path[0]]||[],t[a.path[0]].push(e(a))):r.push(e(a));return{formErrors:r,fieldErrors:t}}get formErrors(){return this.flatten()}}eE.create=e=>new eE(e);let eP=(e,t)=>{let r;switch(e.code){case eV.invalid_type:r=e.received===eZ.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case eV.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(e.expected,s.jsonStringifyReplacer)}`;break;case eV.unrecognized_keys:r=`Unrecognized key(s) in object: ${s.joinValues(e.keys,", ")}`;break;case eV.invalid_union:r="Invalid input";break;case eV.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${s.joinValues(e.options)}`;break;case eV.invalid_enum_value:r=`Invalid enum value. Expected ${s.joinValues(e.options)}, received '${e.received}'`;break;case eV.invalid_arguments:r="Invalid function arguments";break;case eV.invalid_return_type:r="Invalid function return type";break;case eV.invalid_date:r="Invalid date";break;case eV.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(r=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(r=`${r} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?r=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?r=`Invalid input: must end with "${e.validation.endsWith}"`:s.assertNever(e.validation):r="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case eV.too_small:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case eV.too_big:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case eV.custom:r="Invalid input";break;case eV.invalid_intersection_types:r="Intersection results could not be merged";break;case eV.not_multiple_of:r=`Number must be a multiple of ${e.multipleOf}`;break;case eV.not_finite:r="Number must be finite";break;default:r=t.defaultError,s.assertNever(e)}return{message:r}},eD=e=>{let{data:t,path:r,errorMaps:a,issueData:s}=e,i=[...r,...s.path||[]],n={...s,path:i};if(void 0!==s.message)return{...s,path:i,message:s.message};let l="";for(let e of a.filter(e=>!!e).slice().reverse())l=e(n,{data:t,defaultError:l}).message;return{...s,path:i,message:l}};function eI(e,t){let r=eD({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,eP,eP==eP?void 0:eP].filter(e=>!!e)});e.common.issues.push(r)}class eR{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let r=[];for(let a of t){if("aborted"===a.status)return eM;"dirty"===a.status&&e.dirty(),r.push(a.value)}return{status:e.value,value:r}}static async mergeObjectAsync(e,t){let r=[];for(let e of t){let t=await e.key,a=await e.value;r.push({key:t,value:a})}return eR.mergeObjectSync(e,r)}static mergeObjectSync(e,t){let r={};for(let a of t){let{key:t,value:s}=a;if("aborted"===t.status||"aborted"===s.status)return eM;"dirty"===t.status&&e.dirty(),"dirty"===s.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==s.value||a.alwaysSet)&&(r[t.value]=s.value)}return{status:e.value,value:r}}}let eM=Object.freeze({status:"aborted"}),e$=e=>({status:"dirty",value:e}),eL=e=>({status:"valid",value:e}),eU=e=>"aborted"===e.status,ez=e=>"dirty"===e.status,eq=e=>"valid"===e.status,eB=e=>"undefined"!=typeof Promise&&e instanceof Promise;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(n||(n={}));class eW{constructor(e,t,r,a){this._cachedPath=[],this.parent=e,this.data=t,this._path=r,this._key=a}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let eK=(e,t)=>{if(eq(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new eE(e.common.issues);return this._error=t,this._error}}};function eG(e){if(!e)return{};let{errorMap:t,invalid_type_error:r,required_error:a,description:s}=e;if(t&&(r||a))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:s}:{errorMap:(t,s)=>{let{message:i}=e;return"invalid_enum_value"===t.code?{message:i??s.defaultError}:void 0===s.data?{message:i??a??s.defaultError}:"invalid_type"!==t.code?{message:s.defaultError}:{message:i??r??s.defaultError}},description:s}}class eH{get description(){return this._def.description}_getType(e){return eF(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:eF(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new eR,ctx:{common:e.parent.common,data:e.data,parsedType:eF(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(eB(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let r=this.safeParse(e,t);if(r.success)return r.data;throw r.error}safeParse(e,t){let r={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:eF(e)},a=this._parseSync({data:e,path:r.path,parent:r});return eK(r,a)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:eF(e)};if(!this["~standard"].async)try{let r=this._parseSync({data:e,path:[],parent:t});return eq(r)?{value:r.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>eq(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let r=await this.safeParseAsync(e,t);if(r.success)return r.data;throw r.error}async safeParseAsync(e,t){let r={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:eF(e)},a=this._parse({data:e,path:r.path,parent:r});return eK(r,await (eB(a)?a:Promise.resolve(a)))}refine(e,t){let r=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,a)=>{let s=e(t),i=()=>a.addIssue({code:eV.custom,...r(t)});return"undefined"!=typeof Promise&&s instanceof Promise?s.then(e=>!!e||(i(),!1)):!!s||(i(),!1)})}refinement(e,t){return this._refinement((r,a)=>!!e(r)||(a.addIssue("function"==typeof t?t(r,a):t),!1))}_refinement(e){return new tF({schema:this,typeName:l.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return tV.create(this,this._def)}nullable(){return tE.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return tf.create(this)}promise(){return tZ.create(this,this._def)}or(e){return tg.create([this,e],this._def)}and(e){return tx.create(this,e,this._def)}transform(e){return new tF({...eG(this._def),schema:this,typeName:l.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new tP({...eG(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:l.ZodDefault})}brand(){return new tR({typeName:l.ZodBranded,type:this,...eG(this._def)})}catch(e){return new tD({...eG(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:l.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return tM.create(this,e)}readonly(){return t$.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let eJ=/^c[^\s-]{8,}$/i,eY=/^[0-9a-z]+$/,eX=/^[0-9A-HJKMNP-TV-Z]{26}$/i,eQ=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,e0=/^[a-z0-9_-]{21}$/i,e1=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,e2=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,e9=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,e4=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,e6=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,e5=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,e3=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,e8=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,e7=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,te="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",tt=RegExp(`^${te}$`);function tr(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let r=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${r}`}class ta extends eH{_parse(e){var t,r,i,n;let l;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==eZ.string){let t=this._getOrReturnCtx(e);return eI(t,{code:eV.invalid_type,expected:eZ.string,received:t.parsedType}),eM}let d=new eR;for(let o of this._def.checks)if("min"===o.kind)e.data.length<o.value&&(eI(l=this._getOrReturnCtx(e,l),{code:eV.too_small,minimum:o.value,type:"string",inclusive:!0,exact:!1,message:o.message}),d.dirty());else if("max"===o.kind)e.data.length>o.value&&(eI(l=this._getOrReturnCtx(e,l),{code:eV.too_big,maximum:o.value,type:"string",inclusive:!0,exact:!1,message:o.message}),d.dirty());else if("length"===o.kind){let t=e.data.length>o.value,r=e.data.length<o.value;(t||r)&&(l=this._getOrReturnCtx(e,l),t?eI(l,{code:eV.too_big,maximum:o.value,type:"string",inclusive:!0,exact:!0,message:o.message}):r&&eI(l,{code:eV.too_small,minimum:o.value,type:"string",inclusive:!0,exact:!0,message:o.message}),d.dirty())}else if("email"===o.kind)e9.test(e.data)||(eI(l=this._getOrReturnCtx(e,l),{validation:"email",code:eV.invalid_string,message:o.message}),d.dirty());else if("emoji"===o.kind)a||(a=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),a.test(e.data)||(eI(l=this._getOrReturnCtx(e,l),{validation:"emoji",code:eV.invalid_string,message:o.message}),d.dirty());else if("uuid"===o.kind)eQ.test(e.data)||(eI(l=this._getOrReturnCtx(e,l),{validation:"uuid",code:eV.invalid_string,message:o.message}),d.dirty());else if("nanoid"===o.kind)e0.test(e.data)||(eI(l=this._getOrReturnCtx(e,l),{validation:"nanoid",code:eV.invalid_string,message:o.message}),d.dirty());else if("cuid"===o.kind)eJ.test(e.data)||(eI(l=this._getOrReturnCtx(e,l),{validation:"cuid",code:eV.invalid_string,message:o.message}),d.dirty());else if("cuid2"===o.kind)eY.test(e.data)||(eI(l=this._getOrReturnCtx(e,l),{validation:"cuid2",code:eV.invalid_string,message:o.message}),d.dirty());else if("ulid"===o.kind)eX.test(e.data)||(eI(l=this._getOrReturnCtx(e,l),{validation:"ulid",code:eV.invalid_string,message:o.message}),d.dirty());else if("url"===o.kind)try{new URL(e.data)}catch{eI(l=this._getOrReturnCtx(e,l),{validation:"url",code:eV.invalid_string,message:o.message}),d.dirty()}else"regex"===o.kind?(o.regex.lastIndex=0,o.regex.test(e.data)||(eI(l=this._getOrReturnCtx(e,l),{validation:"regex",code:eV.invalid_string,message:o.message}),d.dirty())):"trim"===o.kind?e.data=e.data.trim():"includes"===o.kind?e.data.includes(o.value,o.position)||(eI(l=this._getOrReturnCtx(e,l),{code:eV.invalid_string,validation:{includes:o.value,position:o.position},message:o.message}),d.dirty()):"toLowerCase"===o.kind?e.data=e.data.toLowerCase():"toUpperCase"===o.kind?e.data=e.data.toUpperCase():"startsWith"===o.kind?e.data.startsWith(o.value)||(eI(l=this._getOrReturnCtx(e,l),{code:eV.invalid_string,validation:{startsWith:o.value},message:o.message}),d.dirty()):"endsWith"===o.kind?e.data.endsWith(o.value)||(eI(l=this._getOrReturnCtx(e,l),{code:eV.invalid_string,validation:{endsWith:o.value},message:o.message}),d.dirty()):"datetime"===o.kind?(function(e){let t=`${te}T${tr(e)}`,r=[];return r.push(e.local?"Z?":"Z"),e.offset&&r.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${r.join("|")})`,RegExp(`^${t}$`)})(o).test(e.data)||(eI(l=this._getOrReturnCtx(e,l),{code:eV.invalid_string,validation:"datetime",message:o.message}),d.dirty()):"date"===o.kind?tt.test(e.data)||(eI(l=this._getOrReturnCtx(e,l),{code:eV.invalid_string,validation:"date",message:o.message}),d.dirty()):"time"===o.kind?RegExp(`^${tr(o)}$`).test(e.data)||(eI(l=this._getOrReturnCtx(e,l),{code:eV.invalid_string,validation:"time",message:o.message}),d.dirty()):"duration"===o.kind?e2.test(e.data)||(eI(l=this._getOrReturnCtx(e,l),{validation:"duration",code:eV.invalid_string,message:o.message}),d.dirty()):"ip"===o.kind?(t=e.data,("v4"===(r=o.version)||!r)&&e4.test(t)||("v6"===r||!r)&&e5.test(t)||(eI(l=this._getOrReturnCtx(e,l),{validation:"ip",code:eV.invalid_string,message:o.message}),d.dirty())):"jwt"===o.kind?!function(e,t){if(!e1.test(e))return!1;try{let[r]=e.split("."),a=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),s=JSON.parse(atob(a));if("object"!=typeof s||null===s||"typ"in s&&s?.typ!=="JWT"||!s.alg||t&&s.alg!==t)return!1;return!0}catch{return!1}}(e.data,o.alg)&&(eI(l=this._getOrReturnCtx(e,l),{validation:"jwt",code:eV.invalid_string,message:o.message}),d.dirty()):"cidr"===o.kind?(i=e.data,("v4"===(n=o.version)||!n)&&e6.test(i)||("v6"===n||!n)&&e3.test(i)||(eI(l=this._getOrReturnCtx(e,l),{validation:"cidr",code:eV.invalid_string,message:o.message}),d.dirty())):"base64"===o.kind?e8.test(e.data)||(eI(l=this._getOrReturnCtx(e,l),{validation:"base64",code:eV.invalid_string,message:o.message}),d.dirty()):"base64url"===o.kind?e7.test(e.data)||(eI(l=this._getOrReturnCtx(e,l),{validation:"base64url",code:eV.invalid_string,message:o.message}),d.dirty()):s.assertNever(o);return{status:d.value,value:e.data}}_regex(e,t,r){return this.refinement(t=>e.test(t),{validation:t,code:eV.invalid_string,...n.errToObj(r)})}_addCheck(e){return new ta({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...n.errToObj(e)})}url(e){return this._addCheck({kind:"url",...n.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...n.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...n.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...n.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...n.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...n.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...n.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...n.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...n.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...n.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...n.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...n.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...n.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...n.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...n.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...n.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...n.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...n.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...n.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...n.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...n.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...n.errToObj(t)})}nonempty(e){return this.min(1,n.errToObj(e))}trim(){return new ta({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new ta({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new ta({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}ta.create=e=>new ta({checks:[],typeName:l.ZodString,coerce:e?.coerce??!1,...eG(e)});class ts extends eH{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==eZ.number){let t=this._getOrReturnCtx(e);return eI(t,{code:eV.invalid_type,expected:eZ.number,received:t.parsedType}),eM}let r=new eR;for(let a of this._def.checks)"int"===a.kind?s.isInteger(e.data)||(eI(t=this._getOrReturnCtx(e,t),{code:eV.invalid_type,expected:"integer",received:"float",message:a.message}),r.dirty()):"min"===a.kind?(a.inclusive?e.data<a.value:e.data<=a.value)&&(eI(t=this._getOrReturnCtx(e,t),{code:eV.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty()):"max"===a.kind?(a.inclusive?e.data>a.value:e.data>=a.value)&&(eI(t=this._getOrReturnCtx(e,t),{code:eV.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty()):"multipleOf"===a.kind?0!==function(e,t){let r=(e.toString().split(".")[1]||"").length,a=(t.toString().split(".")[1]||"").length,s=r>a?r:a;return Number.parseInt(e.toFixed(s).replace(".",""))%Number.parseInt(t.toFixed(s).replace(".",""))/10**s}(e.data,a.value)&&(eI(t=this._getOrReturnCtx(e,t),{code:eV.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):"finite"===a.kind?Number.isFinite(e.data)||(eI(t=this._getOrReturnCtx(e,t),{code:eV.not_finite,message:a.message}),r.dirty()):s.assertNever(a);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,n.toString(t))}gt(e,t){return this.setLimit("min",e,!1,n.toString(t))}lte(e,t){return this.setLimit("max",e,!0,n.toString(t))}lt(e,t){return this.setLimit("max",e,!1,n.toString(t))}setLimit(e,t,r,a){return new ts({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:n.toString(a)}]})}_addCheck(e){return new ts({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:n.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:n.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:n.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:n.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:n.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:n.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:n.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:n.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:n.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&s.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let r of this._def.checks){if("finite"===r.kind||"int"===r.kind||"multipleOf"===r.kind)return!0;"min"===r.kind?(null===t||r.value>t)&&(t=r.value):"max"===r.kind&&(null===e||r.value<e)&&(e=r.value)}return Number.isFinite(t)&&Number.isFinite(e)}}ts.create=e=>new ts({checks:[],typeName:l.ZodNumber,coerce:e?.coerce||!1,...eG(e)});class ti extends eH{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==eZ.bigint)return this._getInvalidInput(e);let r=new eR;for(let a of this._def.checks)"min"===a.kind?(a.inclusive?e.data<a.value:e.data<=a.value)&&(eI(t=this._getOrReturnCtx(e,t),{code:eV.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty()):"max"===a.kind?(a.inclusive?e.data>a.value:e.data>=a.value)&&(eI(t=this._getOrReturnCtx(e,t),{code:eV.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty()):"multipleOf"===a.kind?e.data%a.value!==BigInt(0)&&(eI(t=this._getOrReturnCtx(e,t),{code:eV.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):s.assertNever(a);return{status:r.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return eI(t,{code:eV.invalid_type,expected:eZ.bigint,received:t.parsedType}),eM}gte(e,t){return this.setLimit("min",e,!0,n.toString(t))}gt(e,t){return this.setLimit("min",e,!1,n.toString(t))}lte(e,t){return this.setLimit("max",e,!0,n.toString(t))}lt(e,t){return this.setLimit("max",e,!1,n.toString(t))}setLimit(e,t,r,a){return new ti({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:n.toString(a)}]})}_addCheck(e){return new ti({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:n.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:n.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:n.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:n.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:n.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}ti.create=e=>new ti({checks:[],typeName:l.ZodBigInt,coerce:e?.coerce??!1,...eG(e)});class tn extends eH{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==eZ.boolean){let t=this._getOrReturnCtx(e);return eI(t,{code:eV.invalid_type,expected:eZ.boolean,received:t.parsedType}),eM}return eL(e.data)}}tn.create=e=>new tn({typeName:l.ZodBoolean,coerce:e?.coerce||!1,...eG(e)});class tl extends eH{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==eZ.date){let t=this._getOrReturnCtx(e);return eI(t,{code:eV.invalid_type,expected:eZ.date,received:t.parsedType}),eM}if(Number.isNaN(e.data.getTime()))return eI(this._getOrReturnCtx(e),{code:eV.invalid_date}),eM;let r=new eR;for(let a of this._def.checks)"min"===a.kind?e.data.getTime()<a.value&&(eI(t=this._getOrReturnCtx(e,t),{code:eV.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),r.dirty()):"max"===a.kind?e.data.getTime()>a.value&&(eI(t=this._getOrReturnCtx(e,t),{code:eV.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),r.dirty()):s.assertNever(a);return{status:r.value,value:new Date(e.data.getTime())}}_addCheck(e){return new tl({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:n.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:n.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}tl.create=e=>new tl({checks:[],coerce:e?.coerce||!1,typeName:l.ZodDate,...eG(e)});class td extends eH{_parse(e){if(this._getType(e)!==eZ.symbol){let t=this._getOrReturnCtx(e);return eI(t,{code:eV.invalid_type,expected:eZ.symbol,received:t.parsedType}),eM}return eL(e.data)}}td.create=e=>new td({typeName:l.ZodSymbol,...eG(e)});class to extends eH{_parse(e){if(this._getType(e)!==eZ.undefined){let t=this._getOrReturnCtx(e);return eI(t,{code:eV.invalid_type,expected:eZ.undefined,received:t.parsedType}),eM}return eL(e.data)}}to.create=e=>new to({typeName:l.ZodUndefined,...eG(e)});class tu extends eH{_parse(e){if(this._getType(e)!==eZ.null){let t=this._getOrReturnCtx(e);return eI(t,{code:eV.invalid_type,expected:eZ.null,received:t.parsedType}),eM}return eL(e.data)}}tu.create=e=>new tu({typeName:l.ZodNull,...eG(e)});class tc extends eH{constructor(){super(...arguments),this._any=!0}_parse(e){return eL(e.data)}}tc.create=e=>new tc({typeName:l.ZodAny,...eG(e)});class th extends eH{constructor(){super(...arguments),this._unknown=!0}_parse(e){return eL(e.data)}}th.create=e=>new th({typeName:l.ZodUnknown,...eG(e)});class tm extends eH{_parse(e){let t=this._getOrReturnCtx(e);return eI(t,{code:eV.invalid_type,expected:eZ.never,received:t.parsedType}),eM}}tm.create=e=>new tm({typeName:l.ZodNever,...eG(e)});class tp extends eH{_parse(e){if(this._getType(e)!==eZ.undefined){let t=this._getOrReturnCtx(e);return eI(t,{code:eV.invalid_type,expected:eZ.void,received:t.parsedType}),eM}return eL(e.data)}}tp.create=e=>new tp({typeName:l.ZodVoid,...eG(e)});class tf extends eH{_parse(e){let{ctx:t,status:r}=this._processInputParams(e),a=this._def;if(t.parsedType!==eZ.array)return eI(t,{code:eV.invalid_type,expected:eZ.array,received:t.parsedType}),eM;if(null!==a.exactLength){let e=t.data.length>a.exactLength.value,s=t.data.length<a.exactLength.value;(e||s)&&(eI(t,{code:e?eV.too_big:eV.too_small,minimum:s?a.exactLength.value:void 0,maximum:e?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),r.dirty())}if(null!==a.minLength&&t.data.length<a.minLength.value&&(eI(t,{code:eV.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),r.dirty()),null!==a.maxLength&&t.data.length>a.maxLength.value&&(eI(t,{code:eV.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),r.dirty()),t.common.async)return Promise.all([...t.data].map((e,r)=>a.type._parseAsync(new eW(t,e,t.path,r)))).then(e=>eR.mergeArray(r,e));let s=[...t.data].map((e,r)=>a.type._parseSync(new eW(t,e,t.path,r)));return eR.mergeArray(r,s)}get element(){return this._def.type}min(e,t){return new tf({...this._def,minLength:{value:e,message:n.toString(t)}})}max(e,t){return new tf({...this._def,maxLength:{value:e,message:n.toString(t)}})}length(e,t){return new tf({...this._def,exactLength:{value:e,message:n.toString(t)}})}nonempty(e){return this.min(1,e)}}tf.create=(e,t)=>new tf({type:e,minLength:null,maxLength:null,exactLength:null,typeName:l.ZodArray,...eG(t)});class ty extends eH{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=s.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==eZ.object){let t=this._getOrReturnCtx(e);return eI(t,{code:eV.invalid_type,expected:eZ.object,received:t.parsedType}),eM}let{status:t,ctx:r}=this._processInputParams(e),{shape:a,keys:s}=this._getCached(),i=[];if(!(this._def.catchall instanceof tm&&"strip"===this._def.unknownKeys))for(let e in r.data)s.includes(e)||i.push(e);let n=[];for(let e of s){let t=a[e],s=r.data[e];n.push({key:{status:"valid",value:e},value:t._parse(new eW(r,s,r.path,e)),alwaysSet:e in r.data})}if(this._def.catchall instanceof tm){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of i)n.push({key:{status:"valid",value:e},value:{status:"valid",value:r.data[e]}});else if("strict"===e)i.length>0&&(eI(r,{code:eV.unrecognized_keys,keys:i}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of i){let a=r.data[t];n.push({key:{status:"valid",value:t},value:e._parse(new eW(r,a,r.path,t)),alwaysSet:t in r.data})}}return r.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of n){let r=await t.key,a=await t.value;e.push({key:r,value:a,alwaysSet:t.alwaysSet})}return e}).then(e=>eR.mergeObjectSync(t,e)):eR.mergeObjectSync(t,n)}get shape(){return this._def.shape()}strict(e){return n.errToObj,new ty({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,r)=>{let a=this._def.errorMap?.(t,r).message??r.defaultError;return"unrecognized_keys"===t.code?{message:n.errToObj(e).message??a}:{message:a}}}:{}})}strip(){return new ty({...this._def,unknownKeys:"strip"})}passthrough(){return new ty({...this._def,unknownKeys:"passthrough"})}extend(e){return new ty({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new ty({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:l.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new ty({...this._def,catchall:e})}pick(e){let t={};for(let r of s.objectKeys(e))e[r]&&this.shape[r]&&(t[r]=this.shape[r]);return new ty({...this._def,shape:()=>t})}omit(e){let t={};for(let r of s.objectKeys(this.shape))e[r]||(t[r]=this.shape[r]);return new ty({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof ty){let r={};for(let a in t.shape){let s=t.shape[a];r[a]=tV.create(e(s))}return new ty({...t._def,shape:()=>r})}return t instanceof tf?new tf({...t._def,type:e(t.element)}):t instanceof tV?tV.create(e(t.unwrap())):t instanceof tE?tE.create(e(t.unwrap())):t instanceof tb?tb.create(t.items.map(t=>e(t))):t}(this)}partial(e){let t={};for(let r of s.objectKeys(this.shape)){let a=this.shape[r];e&&!e[r]?t[r]=a:t[r]=a.optional()}return new ty({...this._def,shape:()=>t})}required(e){let t={};for(let r of s.objectKeys(this.shape))if(e&&!e[r])t[r]=this.shape[r];else{let e=this.shape[r];for(;e instanceof tV;)e=e._def.innerType;t[r]=e}return new ty({...this._def,shape:()=>t})}keyof(){return tC(s.objectKeys(this.shape))}}ty.create=(e,t)=>new ty({shape:()=>e,unknownKeys:"strip",catchall:tm.create(),typeName:l.ZodObject,...eG(t)}),ty.strictCreate=(e,t)=>new ty({shape:()=>e,unknownKeys:"strict",catchall:tm.create(),typeName:l.ZodObject,...eG(t)}),ty.lazycreate=(e,t)=>new ty({shape:e,unknownKeys:"strip",catchall:tm.create(),typeName:l.ZodObject,...eG(t)});class tg extends eH{_parse(e){let{ctx:t}=this._processInputParams(e),r=this._def.options;if(t.common.async)return Promise.all(r.map(async e=>{let r={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:r}),ctx:r}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let r of e)if("dirty"===r.result.status)return t.common.issues.push(...r.ctx.common.issues),r.result;let r=e.map(e=>new eE(e.ctx.common.issues));return eI(t,{code:eV.invalid_union,unionErrors:r}),eM});{let e;let a=[];for(let s of r){let r={...t,common:{...t.common,issues:[]},parent:null},i=s._parseSync({data:t.data,path:t.path,parent:r});if("valid"===i.status)return i;"dirty"!==i.status||e||(e={result:i,ctx:r}),r.common.issues.length&&a.push(r.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let s=a.map(e=>new eE(e));return eI(t,{code:eV.invalid_union,unionErrors:s}),eM}}get options(){return this._def.options}}tg.create=(e,t)=>new tg({options:e,typeName:l.ZodUnion,...eG(t)});let tv=e=>{if(e instanceof tA)return tv(e.schema);if(e instanceof tF)return tv(e.innerType());if(e instanceof tS)return[e.value];if(e instanceof tO)return e.options;if(e instanceof tT)return s.objectValues(e.enum);if(e instanceof tP)return tv(e._def.innerType);if(e instanceof to)return[void 0];else if(e instanceof tu)return[null];else if(e instanceof tV)return[void 0,...tv(e.unwrap())];else if(e instanceof tE)return[null,...tv(e.unwrap())];else if(e instanceof tR)return tv(e.unwrap());else if(e instanceof t$)return tv(e.unwrap());else if(e instanceof tD)return tv(e._def.innerType);else return[]};class t_ extends eH{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==eZ.object)return eI(t,{code:eV.invalid_type,expected:eZ.object,received:t.parsedType}),eM;let r=this.discriminator,a=t.data[r],s=this.optionsMap.get(a);return s?t.common.async?s._parseAsync({data:t.data,path:t.path,parent:t}):s._parseSync({data:t.data,path:t.path,parent:t}):(eI(t,{code:eV.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),eM)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,r){let a=new Map;for(let r of t){let t=tv(r.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let s of t){if(a.has(s))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(s)}`);a.set(s,r)}}return new t_({typeName:l.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:a,...eG(r)})}}class tx extends eH{_parse(e){let{status:t,ctx:r}=this._processInputParams(e),a=(e,a)=>{if(eU(e)||eU(a))return eM;let i=function e(t,r){let a=eF(t),i=eF(r);if(t===r)return{valid:!0,data:t};if(a===eZ.object&&i===eZ.object){let a=s.objectKeys(r),i=s.objectKeys(t).filter(e=>-1!==a.indexOf(e)),n={...t,...r};for(let a of i){let s=e(t[a],r[a]);if(!s.valid)return{valid:!1};n[a]=s.data}return{valid:!0,data:n}}if(a===eZ.array&&i===eZ.array){if(t.length!==r.length)return{valid:!1};let a=[];for(let s=0;s<t.length;s++){let i=e(t[s],r[s]);if(!i.valid)return{valid:!1};a.push(i.data)}return{valid:!0,data:a}}return a===eZ.date&&i===eZ.date&&+t==+r?{valid:!0,data:t}:{valid:!1}}(e.value,a.value);return i.valid?((ez(e)||ez(a))&&t.dirty(),{status:t.value,value:i.data}):(eI(r,{code:eV.invalid_intersection_types}),eM)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([e,t])=>a(e,t)):a(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}tx.create=(e,t,r)=>new tx({left:e,right:t,typeName:l.ZodIntersection,...eG(r)});class tb extends eH{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==eZ.array)return eI(r,{code:eV.invalid_type,expected:eZ.array,received:r.parsedType}),eM;if(r.data.length<this._def.items.length)return eI(r,{code:eV.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),eM;!this._def.rest&&r.data.length>this._def.items.length&&(eI(r,{code:eV.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let a=[...r.data].map((e,t)=>{let a=this._def.items[t]||this._def.rest;return a?a._parse(new eW(r,e,r.path,t)):null}).filter(e=>!!e);return r.common.async?Promise.all(a).then(e=>eR.mergeArray(t,e)):eR.mergeArray(t,a)}get items(){return this._def.items}rest(e){return new tb({...this._def,rest:e})}}tb.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new tb({items:e,typeName:l.ZodTuple,rest:null,...eG(t)})};class tk extends eH{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==eZ.object)return eI(r,{code:eV.invalid_type,expected:eZ.object,received:r.parsedType}),eM;let a=[],s=this._def.keyType,i=this._def.valueType;for(let e in r.data)a.push({key:s._parse(new eW(r,e,r.path,e)),value:i._parse(new eW(r,r.data[e],r.path,e)),alwaysSet:e in r.data});return r.common.async?eR.mergeObjectAsync(t,a):eR.mergeObjectSync(t,a)}get element(){return this._def.valueType}static create(e,t,r){return new tk(t instanceof eH?{keyType:e,valueType:t,typeName:l.ZodRecord,...eG(r)}:{keyType:ta.create(),valueType:e,typeName:l.ZodRecord,...eG(t)})}}class tw extends eH{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==eZ.map)return eI(r,{code:eV.invalid_type,expected:eZ.map,received:r.parsedType}),eM;let a=this._def.keyType,s=this._def.valueType,i=[...r.data.entries()].map(([e,t],i)=>({key:a._parse(new eW(r,e,r.path,[i,"key"])),value:s._parse(new eW(r,t,r.path,[i,"value"]))}));if(r.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let r of i){let a=await r.key,s=await r.value;if("aborted"===a.status||"aborted"===s.status)return eM;("dirty"===a.status||"dirty"===s.status)&&t.dirty(),e.set(a.value,s.value)}return{status:t.value,value:e}})}{let e=new Map;for(let r of i){let a=r.key,s=r.value;if("aborted"===a.status||"aborted"===s.status)return eM;("dirty"===a.status||"dirty"===s.status)&&t.dirty(),e.set(a.value,s.value)}return{status:t.value,value:e}}}}tw.create=(e,t,r)=>new tw({valueType:t,keyType:e,typeName:l.ZodMap,...eG(r)});class tj extends eH{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==eZ.set)return eI(r,{code:eV.invalid_type,expected:eZ.set,received:r.parsedType}),eM;let a=this._def;null!==a.minSize&&r.data.size<a.minSize.value&&(eI(r,{code:eV.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),t.dirty()),null!==a.maxSize&&r.data.size>a.maxSize.value&&(eI(r,{code:eV.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),t.dirty());let s=this._def.valueType;function i(e){let r=new Set;for(let a of e){if("aborted"===a.status)return eM;"dirty"===a.status&&t.dirty(),r.add(a.value)}return{status:t.value,value:r}}let n=[...r.data.values()].map((e,t)=>s._parse(new eW(r,e,r.path,t)));return r.common.async?Promise.all(n).then(e=>i(e)):i(n)}min(e,t){return new tj({...this._def,minSize:{value:e,message:n.toString(t)}})}max(e,t){return new tj({...this._def,maxSize:{value:e,message:n.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}tj.create=(e,t)=>new tj({valueType:e,minSize:null,maxSize:null,typeName:l.ZodSet,...eG(t)});class tN extends eH{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==eZ.function)return eI(t,{code:eV.invalid_type,expected:eZ.function,received:t.parsedType}),eM;function r(e,r){return eD({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,eP,eP].filter(e=>!!e),issueData:{code:eV.invalid_arguments,argumentsError:r}})}function a(e,r){return eD({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,eP,eP].filter(e=>!!e),issueData:{code:eV.invalid_return_type,returnTypeError:r}})}let s={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof tZ){let e=this;return eL(async function(...t){let n=new eE([]),l=await e._def.args.parseAsync(t,s).catch(e=>{throw n.addIssue(r(t,e)),n}),d=await Reflect.apply(i,this,l);return await e._def.returns._def.type.parseAsync(d,s).catch(e=>{throw n.addIssue(a(d,e)),n})})}{let e=this;return eL(function(...t){let n=e._def.args.safeParse(t,s);if(!n.success)throw new eE([r(t,n.error)]);let l=Reflect.apply(i,this,n.data),d=e._def.returns.safeParse(l,s);if(!d.success)throw new eE([a(l,d.error)]);return d.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new tN({...this._def,args:tb.create(e).rest(th.create())})}returns(e){return new tN({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,r){return new tN({args:e||tb.create([]).rest(th.create()),returns:t||th.create(),typeName:l.ZodFunction,...eG(r)})}}class tA extends eH{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}tA.create=(e,t)=>new tA({getter:e,typeName:l.ZodLazy,...eG(t)});class tS extends eH{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return eI(t,{received:t.data,code:eV.invalid_literal,expected:this._def.value}),eM}return{status:"valid",value:e.data}}get value(){return this._def.value}}function tC(e,t){return new tO({values:e,typeName:l.ZodEnum,...eG(t)})}tS.create=(e,t)=>new tS({value:e,typeName:l.ZodLiteral,...eG(t)});class tO extends eH{_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),r=this._def.values;return eI(t,{expected:s.joinValues(r),received:t.parsedType,code:eV.invalid_type}),eM}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){let t=this._getOrReturnCtx(e),r=this._def.values;return eI(t,{received:t.data,code:eV.invalid_enum_value,options:r}),eM}return eL(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return tO.create(e,{...this._def,...t})}exclude(e,t=this._def){return tO.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}tO.create=tC;class tT extends eH{_parse(e){let t=s.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==eZ.string&&r.parsedType!==eZ.number){let e=s.objectValues(t);return eI(r,{expected:s.joinValues(e),received:r.parsedType,code:eV.invalid_type}),eM}if(this._cache||(this._cache=new Set(s.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){let e=s.objectValues(t);return eI(r,{received:r.data,code:eV.invalid_enum_value,options:e}),eM}return eL(e.data)}get enum(){return this._def.values}}tT.create=(e,t)=>new tT({values:e,typeName:l.ZodNativeEnum,...eG(t)});class tZ extends eH{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==eZ.promise&&!1===t.common.async?(eI(t,{code:eV.invalid_type,expected:eZ.promise,received:t.parsedType}),eM):eL((t.parsedType===eZ.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}tZ.create=(e,t)=>new tZ({type:e,typeName:l.ZodPromise,...eG(t)});class tF extends eH{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===l.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:r}=this._processInputParams(e),a=this._def.effect||null,i={addIssue:e=>{eI(r,e),e.fatal?t.abort():t.dirty()},get path(){return r.path}};if(i.addIssue=i.addIssue.bind(i),"preprocess"===a.type){let e=a.transform(r.data,i);if(r.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return eM;let a=await this._def.schema._parseAsync({data:e,path:r.path,parent:r});return"aborted"===a.status?eM:"dirty"===a.status||"dirty"===t.value?e$(a.value):a});{if("aborted"===t.value)return eM;let a=this._def.schema._parseSync({data:e,path:r.path,parent:r});return"aborted"===a.status?eM:"dirty"===a.status||"dirty"===t.value?e$(a.value):a}}if("refinement"===a.type){let e=e=>{let t=a.refinement(e,i);if(r.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(r=>"aborted"===r.status?eM:("dirty"===r.status&&t.dirty(),e(r.value).then(()=>({status:t.value,value:r.value}))));{let a=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===a.status?eM:("dirty"===a.status&&t.dirty(),e(a.value),{status:t.value,value:a.value})}}if("transform"===a.type){if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(e=>eq(e)?Promise.resolve(a.transform(e.value,i)).then(e=>({status:t.value,value:e})):eM);{let e=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!eq(e))return eM;let s=a.transform(e.value,i);if(s instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:s}}}s.assertNever(a)}}tF.create=(e,t,r)=>new tF({schema:e,typeName:l.ZodEffects,effect:t,...eG(r)}),tF.createWithPreprocess=(e,t,r)=>new tF({schema:t,effect:{type:"preprocess",transform:e},typeName:l.ZodEffects,...eG(r)});class tV extends eH{_parse(e){return this._getType(e)===eZ.undefined?eL(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}tV.create=(e,t)=>new tV({innerType:e,typeName:l.ZodOptional,...eG(t)});class tE extends eH{_parse(e){return this._getType(e)===eZ.null?eL(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}tE.create=(e,t)=>new tE({innerType:e,typeName:l.ZodNullable,...eG(t)});class tP extends eH{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return t.parsedType===eZ.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}tP.create=(e,t)=>new tP({innerType:e,typeName:l.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...eG(t)});class tD extends eH{_parse(e){let{ctx:t}=this._processInputParams(e),r={...t,common:{...t.common,issues:[]}},a=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return eB(a)?a.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new eE(r.common.issues)},input:r.data})})):{status:"valid",value:"valid"===a.status?a.value:this._def.catchValue({get error(){return new eE(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}tD.create=(e,t)=>new tD({innerType:e,typeName:l.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...eG(t)});class tI extends eH{_parse(e){if(this._getType(e)!==eZ.nan){let t=this._getOrReturnCtx(e);return eI(t,{code:eV.invalid_type,expected:eZ.nan,received:t.parsedType}),eM}return{status:"valid",value:e.data}}}tI.create=e=>new tI({typeName:l.ZodNaN,...eG(e)}),Symbol("zod_brand");class tR extends eH{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return this._def.type._parse({data:r,path:t.path,parent:t})}unwrap(){return this._def.type}}class tM extends eH{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?eM:"dirty"===e.status?(t.dirty(),e$(e.value)):this._def.out._parseAsync({data:e.value,path:r.path,parent:r})})();{let e=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?eM:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:r.path,parent:r})}}static create(e,t){return new tM({in:e,out:t,typeName:l.ZodPipeline})}}class t$ extends eH{_parse(e){let t=this._def.innerType._parse(e),r=e=>(eq(e)&&(e.value=Object.freeze(e.value)),e);return eB(t)?t.then(e=>r(e)):r(t)}unwrap(){return this._def.innerType}}t$.create=(e,t)=>new t$({innerType:e,typeName:l.ZodReadonly,...eG(t)}),ty.lazycreate,function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(l||(l={}));let tL=ta.create;ts.create,tI.create,ti.create;let tU=tn.create;tl.create,td.create,to.create,tu.create,tc.create,th.create,tm.create,tp.create;let tz=tf.create,tq=ty.create;ty.strictCreate,tg.create,t_.create,tx.create,tb.create,tk.create,tw.create,tj.create,tN.create,tA.create,tS.create;let tB=tO.create;tT.create,tZ.create,tF.create,tV.create,tE.create,tF.createWithPreprocess,tM.create;var tW=r(2641);function tK(e,t){return(...e)=>{try{return t(...e)}catch{throw Error(void 0)}}}let tG=tK(0,tW.T_);tK(0,tW.Gb);let tH=tq({name:tL().min(2,"Name must be at least 2 characters"),email:tL().email("Please enter a valid email address"),company:tL().optional(),phone:tL().optional(),subject:tL().min(5,"Subject must be at least 5 characters"),message:tL().min(10,"Message must be at least 10 characters"),productInterest:tz(tL()).optional(),preferredContact:tB(["email","phone"]),urgency:tB(["low","medium","high"]),consent:tU().refine(e=>!0===e,"You must agree to the privacy policy")});function tJ({className:e="",onSuccess:t}){var r;let a=tG("contact.form"),s=(0,tW.bU)(),[i,n]=(0,o.useState)(!1),[l,p]=(0,o.useState)("idle"),[g,_]=(0,o.useState)(""),{register:A,handleSubmit:S,formState:{errors:Z},reset:D,watch:M}=function(e={}){let t=u().useRef(void 0),r=u().useRef(void 0),[a,s]=u().useState({isDirty:!1,isValidating:!1,isLoading:z(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:z(e.defaultValues)?void 0:e.defaultValues});!t.current&&(t.current={...e.formControl?e.formControl:function(e={}){let t,r={...eN,...e},a={submitCount:0,isDirty:!1,isReady:!1,isLoading:z(r.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:r.errors||{},disabled:r.disabled||!1},s={},i=(f(r.defaultValues)||f(r.values))&&b(r.defaultValues||r.values)||{},n=r.shouldUnregister?{}:b(i),l={action:!1,mount:!1,watch:!1},d={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},o=0,u={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},p={...u},g={array:R(),state:R()},_=r.criteriaMode===T.all,A=e=>t=>{clearTimeout(o),o=setTimeout(e,t)},S=async e=>{if(!r.disabled&&(u.isValid||p.isValid||e)){let e=r.resolver?L((await W()).errors):await Y(s,!0);e!==a.isValid&&g.state.next({isValid:e})}},Z=(e,t)=>{!r.disabled&&(u.isValidating||u.validatingFields||p.isValidating||p.validatingFields)&&((e||Array.from(d.mount)).forEach(e=>{e&&(t?C(a.validatingFields,e,t):H(a.validatingFields,e))}),g.state.next({validatingFields:a.validatingFields,isValidating:!L(a.validatingFields)}))},F=(e,t)=>{C(a.errors,e,t),g.state.next({errors:a.errors})},V=(e,t,r,a)=>{let d=j(s,e);if(d){let s=j(n,e,w(r)?j(i,e):r);w(s)||a&&a.defaultChecked||t?C(n,e,t?s:ei(d._f)):et(e,s),l.mount&&S()}},D=(e,t,s,n,l)=>{let d=!1,o=!1,c={name:e};if(!r.disabled){if(!s||n){(u.isDirty||p.isDirty)&&(o=a.isDirty,a.isDirty=c.isDirty=Q(),d=o!==c.isDirty);let r=$(j(i,e),t);o=!!j(a.dirtyFields,e),r?H(a.dirtyFields,e):C(a.dirtyFields,e,!0),c.dirtyFields=a.dirtyFields,d=d||(u.dirtyFields||p.dirtyFields)&&!r!==o}if(s){let t=j(a.touchedFields,e);t||(C(a.touchedFields,e,s),c.touchedFields=a.touchedFields,d=d||(u.touchedFields||p.touchedFields)&&t!==s)}d&&l&&g.state.next(c)}return d?c:{}},M=(e,s,i,n)=>{let l=j(a.errors,e),d=(u.isValid||p.isValid)&&N(s)&&a.isValid!==s;if(r.delayError&&i?(t=A(()=>F(e,i)))(r.delayError):(clearTimeout(o),t=null,i?C(a.errors,e,i):H(a.errors,e)),(i?!$(l,i):l)||!L(n)||d){let t={...n,...d&&N(s)?{isValid:s}:{},errors:a.errors,name:e};a={...a,...t},g.state.next(t)}},W=async e=>{Z(e,!0);let t=await r.resolver(n,r.context,en(e||d.mount,s,r.criteriaMode,r.shouldUseNativeValidation));return Z(e),t},J=async e=>{let{errors:t}=await W(e);if(e)for(let r of e){let e=j(t,r);e?C(a.errors,r,e):H(a.errors,r)}else a.errors=t;return t},Y=async(e,t,s={valid:!0})=>{for(let i in e){let l=e[i];if(l){let{_f:e,...o}=l;if(e){let o=d.array.has(e.name),c=l._f&&ec(l._f);c&&u.validatingFields&&Z([i],!0);let h=await ej(l,d.disabled,n,_,r.shouldUseNativeValidation&&!t,o);if(c&&u.validatingFields&&Z([i]),h[e.name]&&(s.valid=!1,t))break;t||(j(h,e.name)?o?ex(a.errors,h,e.name):C(a.errors,e.name,h[e.name]):H(a.errors,e.name))}L(o)||await Y(o,t,s)}}return s.valid},Q=(e,t)=>!r.disabled&&(e&&t&&C(n,e,t),!$(ek(),i)),ee=(e,t,r)=>P(e,d,{...l.mount?n:w(t)?i:E(e)?{[e]:t}:t},r,t),et=(e,t,r={})=>{let a=j(s,e),i=t;if(a){let r=a._f;r&&(r.disabled||C(n,e,er(t,r)),i=q(r.ref)&&m(t)?"":t,B(r.ref)?[...r.ref.options].forEach(e=>e.selected=i.includes(e.value)):r.refs?c(r.ref)?r.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(i)?e.checked=!!i.find(t=>t===e.value):e.checked=i===e.value||!!i)}):r.refs.forEach(e=>e.checked=e.value===i):U(r.ref)?r.ref.value="":(r.ref.value=i,r.ref.type||g.state.next({name:e,values:b(n)})))}(r.shouldDirty||r.shouldTouch)&&D(e,i,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&eb(e)},ea=(e,t,r)=>{for(let a in t){if(!t.hasOwnProperty(a))return;let i=t[a],n=e+"."+a,l=j(s,n);(d.array.has(e)||f(i)||l&&!l._f)&&!h(i)?ea(n,i,r):et(n,i,r)}},es=(e,t,r={})=>{let o=j(s,e),c=d.array.has(e),h=b(t);C(n,e,h),c?(g.array.next({name:e,values:b(n)}),(u.isDirty||u.dirtyFields||p.isDirty||p.dirtyFields)&&r.shouldDirty&&g.state.next({name:e,dirtyFields:X(i,n),isDirty:Q(e,h)})):!o||o._f||m(h)?et(e,h,r):ea(e,h,r),em(e,d)&&g.state.next({...a}),g.state.next({name:l.mount?e:void 0,values:b(n)})},el=async e=>{l.mount=!0;let i=e.target,o=i.name,c=!0,m=j(s,o),f=e=>{c=Number.isNaN(e)||h(e)&&isNaN(e.getTime())||$(e,j(n,o,e))},v=eo(r.mode),x=eo(r.reValidateMode);if(m){let l,h;let k=i.type?ei(m._f):y(e),w=e.type===O.BLUR||e.type===O.FOCUS_OUT,N=!eh(m._f)&&!r.resolver&&!j(a.errors,o)&&!m._f.deps||ev(w,j(a.touchedFields,o),a.isSubmitted,x,v),A=em(o,d,w);C(n,o,k),w?(m._f.onBlur&&m._f.onBlur(e),t&&t(0)):m._f.onChange&&m._f.onChange(e);let T=D(o,k,w),F=!L(T)||A;if(w||g.state.next({name:o,type:e.type,values:b(n)}),N)return(u.isValid||p.isValid)&&("onBlur"===r.mode?w&&S():w||S()),F&&g.state.next({name:o,...A?{}:T});if(!w&&A&&g.state.next({...a}),r.resolver){let{errors:e}=await W([o]);if(f(k),c){let t=ef(a.errors,s,o),r=ef(e,s,t.name||o);l=r.error,o=r.name,h=L(e)}}else Z([o],!0),l=(await ej(m,d.disabled,n,_,r.shouldUseNativeValidation))[o],Z([o]),f(k),c&&(l?h=!1:(u.isValid||p.isValid)&&(h=await Y(s,!0)));c&&(m._f.deps&&eb(m._f.deps),M(o,h,l,T))}},eu=(e,t)=>{if(j(a.errors,t)&&e.focus)return e.focus(),1},eb=async(e,t={})=>{let i,n;let l=I(e);if(r.resolver){let t=await J(w(e)?e:l);i=L(t),n=e?!l.some(e=>j(t,e)):i}else e?((n=(await Promise.all(l.map(async e=>{let t=j(s,e);return await Y(t&&t._f?{[e]:t}:t)}))).every(Boolean))||a.isValid)&&S():n=i=await Y(s);return g.state.next({...!E(e)||(u.isValid||p.isValid)&&i!==a.isValid?{}:{name:e},...r.resolver||!e?{isValid:i}:{},errors:a.errors}),t.shouldFocus&&!n&&ep(s,eu,e?l:d.mount),n},ek=e=>{let t={...l.mount?n:i};return w(e)?t:E(e)?j(t,e):e.map(e=>j(t,e))},ew=(e,t)=>({invalid:!!j((t||a).errors,e),isDirty:!!j((t||a).dirtyFields,e),error:j((t||a).errors,e),isValidating:!!j(a.validatingFields,e),isTouched:!!j((t||a).touchedFields,e)}),eA=(e,t,r)=>{let i=(j(s,e,{_f:{}})._f||{}).ref,{ref:n,message:l,type:d,...o}=j(a.errors,e)||{};C(a.errors,e,{...o,...t,ref:i}),g.state.next({name:e,errors:a.errors,isValid:!1}),r&&r.shouldFocus&&i&&i.focus&&i.focus()},eS=e=>g.state.subscribe({next:t=>{eg(e.name,t.name,e.exact)&&ey(t,e.formState||u,eP,e.reRenderRoot)&&e.callback({values:{...n},...a,...t})}}).unsubscribe,eC=(e,t={})=>{for(let l of e?I(e):d.mount)d.mount.delete(l),d.array.delete(l),t.keepValue||(H(s,l),H(n,l)),t.keepError||H(a.errors,l),t.keepDirty||H(a.dirtyFields,l),t.keepTouched||H(a.touchedFields,l),t.keepIsValidating||H(a.validatingFields,l),r.shouldUnregister||t.keepDefaultValue||H(i,l);g.state.next({values:b(n)}),g.state.next({...a,...t.keepDirty?{isDirty:Q()}:{}}),t.keepIsValid||S()},eO=({disabled:e,name:t})=>{(N(e)&&l.mount||e||d.disabled.has(t))&&(e?d.disabled.add(t):d.disabled.delete(t))},eT=(e,t={})=>{let a=j(s,e),n=N(t.disabled)||N(r.disabled);return C(s,e,{...a||{},_f:{...a&&a._f?a._f:{ref:{name:e}},name:e,mount:!0,...t}}),d.mount.add(e),a?eO({disabled:N(t.disabled)?t.disabled:r.disabled,name:e}):V(e,!0,t.value),{...n?{disabled:t.disabled||r.disabled}:{},...r.progressive?{required:!!t.required,min:ed(t.min),max:ed(t.max),minLength:ed(t.minLength),maxLength:ed(t.maxLength),pattern:ed(t.pattern)}:{},name:e,onChange:el,onBlur:el,ref:n=>{if(n){eT(e,t),a=j(s,e);let r=w(n.value)&&n.querySelectorAll&&n.querySelectorAll("input,select,textarea")[0]||n,l=K(r),d=a._f.refs||[];(l?d.find(e=>e===r):r===a._f.ref)||(C(s,e,{_f:{...a._f,...l?{refs:[...d.filter(G),r,...Array.isArray(j(i,e))?[{}]:[]],ref:{type:r.type,name:e}}:{ref:r}}}),V(e,!1,void 0,r))}else(a=j(s,e,{}))._f&&(a._f.mount=!1),(r.shouldUnregister||t.shouldUnregister)&&!(v(d.array,e)&&l.action)&&d.unMount.add(e)}}},eZ=()=>r.shouldFocusError&&ep(s,eu,d.mount),eF=(e,t)=>async i=>{let l;i&&(i.preventDefault&&i.preventDefault(),i.persist&&i.persist());let o=b(n);if(g.state.next({isSubmitting:!0}),r.resolver){let{errors:e,values:t}=await W();a.errors=e,o=t}else await Y(s);if(d.disabled.size)for(let e of d.disabled)C(o,e,void 0);if(H(a.errors,"root"),L(a.errors)){g.state.next({errors:{}});try{await e(o,i)}catch(e){l=e}}else t&&await t({...a.errors},i),eZ(),setTimeout(eZ);if(g.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:L(a.errors)&&!l,submitCount:a.submitCount+1,errors:a.errors}),l)throw l},eV=(e,t={})=>{let o=e?b(e):i,c=b(o),h=L(e),m=h?i:c;if(t.keepDefaultValues||(i=o),!t.keepValues){if(t.keepDirtyValues)for(let e of Array.from(new Set([...d.mount,...Object.keys(X(i,n))])))j(a.dirtyFields,e)?C(m,e,j(n,e)):es(e,j(m,e));else{if(x&&w(e))for(let e of d.mount){let t=j(s,e);if(t&&t._f){let e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(q(e)){let t=e.closest("form");if(t){t.reset();break}}}}for(let e of d.mount)es(e,j(m,e))}n=b(m),g.array.next({values:{...m}}),g.state.next({values:{...m}})}d={mount:t.keepDirtyValues?d.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},l.mount=!u.isValid||!!t.keepIsValid||!!t.keepDirtyValues,l.watch=!!r.shouldUnregister,g.state.next({submitCount:t.keepSubmitCount?a.submitCount:0,isDirty:!h&&(t.keepDirty?a.isDirty:!!(t.keepDefaultValues&&!$(e,i))),isSubmitted:!!t.keepIsSubmitted&&a.isSubmitted,dirtyFields:h?{}:t.keepDirtyValues?t.keepDefaultValues&&n?X(i,n):a.dirtyFields:t.keepDefaultValues&&e?X(i,e):t.keepDirty?a.dirtyFields:{},touchedFields:t.keepTouched?a.touchedFields:{},errors:t.keepErrors?a.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&a.isSubmitSuccessful,isSubmitting:!1})},eE=(e,t)=>eV(z(e)?e(n):e,t),eP=e=>{a={...a,...e}},eD={control:{register:eT,unregister:eC,getFieldState:ew,handleSubmit:eF,setError:eA,_subscribe:eS,_runSchema:W,_focusError:eZ,_getWatch:ee,_getDirty:Q,_setValid:S,_setFieldArray:(e,t=[],d,o,c=!0,h=!0)=>{if(o&&d&&!r.disabled){if(l.action=!0,h&&Array.isArray(j(s,e))){let t=d(j(s,e),o.argA,o.argB);c&&C(s,e,t)}if(h&&Array.isArray(j(a.errors,e))){let t=d(j(a.errors,e),o.argA,o.argB);c&&C(a.errors,e,t),e_(a.errors,e)}if((u.touchedFields||p.touchedFields)&&h&&Array.isArray(j(a.touchedFields,e))){let t=d(j(a.touchedFields,e),o.argA,o.argB);c&&C(a.touchedFields,e,t)}(u.dirtyFields||p.dirtyFields)&&(a.dirtyFields=X(i,n)),g.state.next({name:e,isDirty:Q(e,t),dirtyFields:a.dirtyFields,errors:a.errors,isValid:a.isValid})}else C(n,e,t)},_setDisabledField:eO,_setErrors:e=>{a.errors=e,g.state.next({errors:a.errors,isValid:!1})},_getFieldArray:e=>k(j(l.mount?n:i,e,r.shouldUnregister?j(i,e,[]):[])),_reset:eV,_resetDefaultValues:()=>z(r.defaultValues)&&r.defaultValues().then(e=>{eE(e,r.resetOptions),g.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let e of d.unMount){let t=j(s,e);t&&(t._f.refs?t._f.refs.every(e=>!G(e)):!G(t._f.ref))&&eC(e)}d.unMount=new Set},_disableForm:e=>{N(e)&&(g.state.next({disabled:e}),ep(s,(t,r)=>{let a=j(s,r);a&&(t.disabled=a._f.disabled||e,Array.isArray(a._f.refs)&&a._f.refs.forEach(t=>{t.disabled=a._f.disabled||e}))},0,!1))},_subjects:g,_proxyFormState:u,get _fields(){return s},get _formValues(){return n},get _state(){return l},set _state(value){l=value},get _defaultValues(){return i},get _names(){return d},set _names(value){d=value},get _formState(){return a},get _options(){return r},set _options(value){r={...r,...value}}},subscribe:e=>(l.mount=!0,p={...p,...e.formState},eS({...e,formState:p})),trigger:eb,register:eT,handleSubmit:eF,watch:(e,t)=>z(e)?g.state.subscribe({next:r=>e(ee(void 0,t),r)}):ee(e,t,!0),setValue:es,getValues:ek,reset:eE,resetField:(e,t={})=>{j(s,e)&&(w(t.defaultValue)?es(e,b(j(i,e))):(es(e,t.defaultValue),C(i,e,b(t.defaultValue))),t.keepTouched||H(a.touchedFields,e),t.keepDirty||(H(a.dirtyFields,e),a.isDirty=t.defaultValue?Q(e,b(j(i,e))):Q()),!t.keepError&&(H(a.errors,e),u.isValid&&S()),g.state.next({...a}))},clearErrors:e=>{e&&I(e).forEach(e=>H(a.errors,e)),g.state.next({errors:e?a.errors:{}})},unregister:eC,setError:eA,setFocus:(e,t={})=>{let r=j(s,e),a=r&&r._f;if(a){let e=a.refs?a.refs[0]:a.ref;e.focus&&(e.focus(),t.shouldSelect&&z(e.select)&&e.select())}},getFieldState:ew};return{...eD,formControl:eD}}(e),formState:a},e.formControl&&e.defaultValues&&!z(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions));let i=t.current.control;return i._options=e,V(()=>{let e=i._subscribe({formState:i._proxyFormState,callback:()=>s({...i._formState}),reRenderRoot:!0});return s(e=>({...e,isReady:!0})),i._formState.isReady=!0,e},[i]),u().useEffect(()=>i._disableForm(e.disabled),[i,e.disabled]),u().useEffect(()=>{e.mode&&(i._options.mode=e.mode),e.reValidateMode&&(i._options.reValidateMode=e.reValidateMode)},[i,e.mode,e.reValidateMode]),u().useEffect(()=>{e.errors&&(i._setErrors(e.errors),i._focusError())},[i,e.errors]),u().useEffect(()=>{e.shouldUnregister&&i._subjects.state.next({values:i._getWatch()})},[i,e.shouldUnregister]),u().useEffect(()=>{if(i._proxyFormState.isDirty){let e=i._getDirty();e!==a.isDirty&&i._subjects.state.next({isDirty:e})}},[i,a.isDirty]),u().useEffect(()=>{e.values&&!$(e.values,r.current)?(i._reset(e.values,i._options.resetOptions),r.current=e.values,s(e=>({...e}))):i._resetDefaultValues()},[i,e.values]),u().useEffect(()=>{i._state.mount||(i._setValid(),i._state.mount=!0),i._state.watch&&(i._state.watch=!1,i._subjects.state.next({...i._formState})),i._removeUnmounted()}),t.current.formState=F(a,i),t.current}({resolver:(void 0===r&&(r={}),function(e,t,a){try{return Promise.resolve(function(t,s){try{var i=Promise.resolve(tH["sync"===r.mode?"parse":"parseAsync"](e,void 0)).then(function(t){return a.shouldUseNativeValidation&&eS({},a),{errors:{},values:r.raw?e:t}})}catch(e){return s(e)}return i&&i.then?i.then(void 0,s):i}(0,function(e){if(Array.isArray(null==e?void 0:e.errors))return{values:{},errors:eC(eT(e.errors,!a.shouldUseNativeValidation&&"all"===a.criteriaMode),a)};throw e}))}catch(e){return Promise.reject(e)}}),defaultValues:{preferredContact:"email",urgency:"medium",productInterest:[]}}),W=M("preferredContact"),J=async e=>{n(!0),p("idle"),_("");try{let r=await fetch("/api/contact",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,locale:s||"en"})}),a=await r.json();a.success?(p("success"),D(),t?.()):(p("error"),_(a.error||"An error occurred while sending your message"))}catch(e){p("error"),_("Network error. Please try again.")}finally{n(!1)}},Y=tG("products.interests"),Q=tG("products.services"),ee=[{value:"flood-barriers",label:Y("floodBarriers")},{value:"water-pumps",label:Y("waterPumps")},{value:"drainage-systems",label:Y("drainageSystems")},{value:"emergency-equipment",label:Y("emergencyEquipment")},{value:"consulting-services",label:Q("consulting")},{value:"maintenance-support",label:Q("maintenance")}];return d.jsx("div",{className:`max-w-2xl mx-auto ${e}`,children:(0,d.jsxs)("form",{onSubmit:S(J),className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:[a("name")," *"]}),d.jsx("input",{...A("name"),type:"text",id:"name",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:a("namePlaceholder")}),Z.name&&d.jsx("p",{className:"mt-1 text-sm text-red-600",children:Z.name.message})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:[a("email")," *"]}),d.jsx("input",{...A("email"),type:"email",id:"email",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:a("emailPlaceholder")}),Z.email&&d.jsx("p",{className:"mt-1 text-sm text-red-600",children:Z.email.message})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[d.jsx("label",{htmlFor:"company",className:"block text-sm font-medium text-gray-700 mb-2",children:a("company")}),d.jsx("input",{...A("company"),type:"text",id:"company",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:a("companyPlaceholder")})]}),(0,d.jsxs)("div",{children:[d.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:a("phone")}),d.jsx("input",{...A("phone"),type:"tel",id:"phone",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:a("phonePlaceholder")})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:[a("subject")," *"]}),d.jsx("input",{...A("subject"),type:"text",id:"subject",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:a("subjectPlaceholder")}),Z.subject&&d.jsx("p",{className:"mt-1 text-sm text-red-600",children:Z.subject.message})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:[a("message")," *"]}),d.jsx("textarea",{...A("message"),id:"message",rows:5,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:a("messagePlaceholder")}),Z.message&&d.jsx("p",{className:"mt-1 text-sm text-red-600",children:Z.message.message})]}),(0,d.jsxs)("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:a("productInterest")}),d.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:ee.map(e=>(0,d.jsxs)("label",{className:"flex items-center",children:[d.jsx("input",{...A("productInterest"),type:"checkbox",value:e.value,className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),d.jsx("span",{className:"ml-2 text-sm text-gray-700",children:e.label})]},e.value))})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:[a("preferredContact")," *"]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("label",{className:"flex items-center",children:[d.jsx("input",{...A("preferredContact"),type:"radio",value:"email",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),d.jsx("span",{className:"ml-2 text-sm text-gray-700",children:a("email")})]}),(0,d.jsxs)("label",{className:"flex items-center",children:[d.jsx("input",{...A("preferredContact"),type:"radio",value:"phone",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),d.jsx("span",{className:"ml-2 text-sm text-gray-700",children:a("phone")})]})]}),"phone"===W&&!M("phone")&&d.jsx("p",{className:"mt-1 text-sm text-amber-600",children:a("phoneRequired")})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{htmlFor:"urgency",className:"block text-sm font-medium text-gray-700 mb-2",children:[a("urgency")," *"]}),(0,d.jsxs)("select",{...A("urgency"),id:"urgency",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[d.jsx("option",{value:"low",children:a("urgencyLow")}),d.jsx("option",{value:"medium",children:a("urgencyMedium")}),d.jsx("option",{value:"high",children:a("urgencyHigh")})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"flex items-start",children:[d.jsx("input",{...A("consent"),type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"}),(0,d.jsxs)("span",{className:"ml-2 text-sm text-gray-700",children:[a("consent")," ",d.jsx("a",{href:"/privacy",className:"text-blue-600 hover:text-blue-800 underline",children:a("privacyPolicy")})]})]}),Z.consent&&d.jsx("p",{className:"mt-1 text-sm text-red-600",children:Z.consent.message})]}),"success"===l&&d.jsx("div",{className:"p-4 bg-green-50 border border-green-200 rounded-md",children:d.jsx("p",{className:"text-green-800",children:a("successMessage")})}),"error"===l&&d.jsx("div",{className:"p-4 bg-red-50 border border-red-200 rounded-md",children:d.jsx("p",{className:"text-red-800",children:g||a("errorMessage")})}),d.jsx("div",{children:d.jsx("button",{type:"submit",disabled:i,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:a(i?"sending":"submit")})})]})})}},3498:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,generateMetadata:()=>u});var a=r(8435);let s=(0,r(2447).createProxy)(String.raw`/Users/<USER>/Vibe-Code/test-web-1.0/src/components/forms/ContactForm.tsx#default`);var i=r(3561);let n=(0,i.Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);var l=r(7996);let d=(0,i.Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),o=(0,i.Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);async function u({params:e}){return{title:"Contact Us - Tucsenberg",description:"Get in touch with Tucsenberg for flood protection solutions. Contact our expert team for inquiries about flood barriers, water pumps, and emergency equipment.",keywords:"contact, flood protection, emergency equipment, consultation, Tucsenberg",openGraph:{title:"Contact Tucsenberg - Flood Protection Experts",description:"Contact our team for professional flood protection solutions and emergency equipment.",type:"website"}}}function c({params:e}){return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("section",{className:"bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16",children:a.jsx("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[a.jsx("h1",{className:"text-4xl md:text-5xl font-bold mb-6",children:"Contact Our Experts"}),a.jsx("p",{className:"text-xl md:text-2xl text-blue-100 mb-8",children:"Ready to protect your property from flooding? Get in touch with our team for professional consultation and solutions."})]})})}),a.jsx("section",{className:"py-16",children:a.jsx("div",{className:"container mx-auto px-4",children:a.jsx("div",{className:"max-w-6xl mx-auto",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Get in Touch"}),a.jsx("p",{className:"text-lg text-gray-600 mb-8",children:"Our team of flood protection experts is ready to help you find the right solution for your needs. Whether you're dealing with an emergency or planning ahead, we're here to assist."})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx(n,{className:"h-6 w-6 text-blue-600 mt-1"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Phone"}),a.jsx("p",{className:"text-gray-600",children:"+****************"}),a.jsx("p",{className:"text-sm text-gray-500",children:"24/7 Emergency Hotline"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx(l.Z,{className:"h-6 w-6 text-blue-600 mt-1"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Email"}),a.jsx("p",{className:"text-gray-600",children:"<EMAIL>"}),a.jsx("p",{className:"text-sm text-gray-500",children:"We respond within 24 hours"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx(d,{className:"h-6 w-6 text-blue-600 mt-1"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Address"}),(0,a.jsxs)("p",{className:"text-gray-600",children:["123 Flood Protection Ave",a.jsx("br",{}),"Safety City, SC 12345",a.jsx("br",{}),"United States"]})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx(o,{className:"h-6 w-6 text-blue-600 mt-1"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Business Hours"}),(0,a.jsxs)("div",{className:"text-gray-600",children:[a.jsx("p",{children:"Monday - Friday: 8:00 AM - 6:00 PM"}),a.jsx("p",{children:"Saturday: 9:00 AM - 4:00 PM"}),a.jsx("p",{children:"Sunday: Emergency calls only"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-red-800 mb-2",children:"\uD83D\uDEA8 Emergency Situations"}),(0,a.jsxs)("p",{className:"text-red-700",children:["If you're experiencing active flooding or an emergency situation, please call our 24/7 emergency hotline immediately at"," ",a.jsx("strong",{children:"+1 (555) 911-FLOOD"})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-blue-800 mb-3",children:"Service Areas"}),(0,a.jsxs)("div",{className:"text-blue-700",children:[a.jsx("p",{className:"mb-2",children:"We provide services across:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[a.jsx("li",{children:"North America (US & Canada)"}),a.jsx("li",{children:"Europe (EU countries)"}),a.jsx("li",{children:"Asia-Pacific region"}),a.jsx("li",{children:"Emergency response worldwide"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Send us a Message"}),a.jsx(s,{onSuccess:()=>{}})]})]})})})}),a.jsx("section",{className:"bg-white py-16",children:a.jsx("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[a.jsx("h2",{className:"text-3xl font-bold text-gray-900 text-center mb-12",children:"Frequently Asked Questions"}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"How quickly can you respond to emergency situations?"}),a.jsx("p",{className:"text-gray-600",children:"Our emergency response team is available 24/7 and can typically be on-site within 2-4 hours for critical situations, depending on your location."})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Do you provide international services?"}),a.jsx("p",{className:"text-gray-600",children:"Yes, we provide flood protection solutions worldwide. Our international team can assist with both emergency response and planned installations."})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"What information should I include in my inquiry?"}),a.jsx("p",{className:"text-gray-600",children:"Please include details about your location, the type of flooding risk, timeline requirements, and any specific products or services you're interested in."})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Do you offer free consultations?"}),a.jsx("p",{className:"text-gray-600",children:"Yes, we provide free initial consultations to assess your flood protection needs and recommend appropriate solutions."})]})]})]})})})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[999,365,811,539],()=>r(5094));module.exports=a})();