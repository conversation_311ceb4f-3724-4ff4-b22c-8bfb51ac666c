import { useTranslations, useLocale } from 'next-intl'
import { locales, defaultLocale, type Locale, LOCALE_CONFIG } from '../i18n'

// 重新导出类型和配置以保持兼容性
export type SupportedLocale = Locale
export const SUPPORTED_LOCALES = LOCALE_CONFIG
export const DEFAULT_LOCALE = defaultLocale

// 语言检测和切换工具
export class I18nUtils {
  /**
   * 获取当前语言信息（仅在服务端使用）
   */
  static getCurrentLocale(): SupportedLocale {
    if (typeof window === 'undefined') return DEFAULT_LOCALE

    // 在客户端，应该使用 useI18n Hook 来获取当前语言
    return DEFAULT_LOCALE
  }

  /**
   * 获取语言配置
   */
  static getLocaleConfig(locale: SupportedLocale) {
    return SUPPORTED_LOCALES[locale] || SUPPORTED_LOCALES[DEFAULT_LOCALE]
  }

  /**
   * 获取所有支持的语言
   */
  static getSupportedLocales() {
    return Object.keys(SUPPORTED_LOCALES) as SupportedLocale[]
  }

  /**
   * 检查是否为支持的语言
   */
  static isSupportedLocale(locale: string): locale is SupportedLocale {
    return locale in SUPPORTED_LOCALES
  }

  /**
   * 切换语言（仅在服务端使用，客户端请使用 useI18n Hook）
   */
  static switchLocale(locale: SupportedLocale, asPath?: string) {
    if (typeof window === 'undefined') return

    // 在客户端，应该使用 useI18n Hook 来切换语言
    console.warn('I18nUtils.switchLocale should not be used on client side. Use useI18n hook instead.')
  }

  /**
   * 获取本地化的URL
   */
  static getLocalizedUrl(path: string, locale: SupportedLocale) {
    if (locale === DEFAULT_LOCALE) {
      return path
    }
    return `/${locale}${path}`
  }

  /**
   * 从URL中提取语言
   */
  static extractLocaleFromPath(path: string): { locale: SupportedLocale; cleanPath: string } {
    const segments = path.split('/').filter(Boolean)
    const firstSegment = segments[0]

    if (firstSegment && this.isSupportedLocale(firstSegment)) {
      return {
        locale: firstSegment,
        cleanPath: '/' + segments.slice(1).join('/')
      }
    }

    return {
      locale: DEFAULT_LOCALE,
      cleanPath: path
    }
  }
}

// 格式化工具
export class FormatUtils {
  /**
   * 格式化货币
   */
  static formatCurrency(
    amount: number, 
    locale: SupportedLocale = DEFAULT_LOCALE,
    currency?: string
  ): string {
    const config = SUPPORTED_LOCALES[locale]
    const currencyCode = currency || config.currency
    
    return new Intl.NumberFormat(config.numberFormat, {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  /**
   * 格式化数字
   */
  static formatNumber(
    number: number,
    locale: SupportedLocale = DEFAULT_LOCALE,
    options?: Intl.NumberFormatOptions
  ): string {
    const config = SUPPORTED_LOCALES[locale]
    
    return new Intl.NumberFormat(config.numberFormat, options).format(number)
  }

  /**
   * 格式化日期
   */
  static formatDate(
    date: Date | string,
    locale: SupportedLocale = DEFAULT_LOCALE,
    options?: Intl.DateTimeFormatOptions
  ): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    const config = SUPPORTED_LOCALES[locale]
    
    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }
    
    return new Intl.DateTimeFormat(config.numberFormat, {
      ...defaultOptions,
      ...options
    }).format(dateObj)
  }

  /**
   * 格式化相对时间
   */
  static formatRelativeTime(
    date: Date | string,
    locale: SupportedLocale = DEFAULT_LOCALE
  ): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)
    
    const config = SUPPORTED_LOCALES[locale]
    const rtf = new Intl.RelativeTimeFormat(config.numberFormat, { numeric: 'auto' })
    
    // 计算时间差
    if (diffInSeconds < 60) {
      return rtf.format(-diffInSeconds, 'second')
    } else if (diffInSeconds < 3600) {
      return rtf.format(-Math.floor(diffInSeconds / 60), 'minute')
    } else if (diffInSeconds < 86400) {
      return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour')
    } else if (diffInSeconds < 2592000) {
      return rtf.format(-Math.floor(diffInSeconds / 86400), 'day')
    } else if (diffInSeconds < 31536000) {
      return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month')
    } else {
      return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year')
    }
  }
}

// next-intl 翻译工具Hook
export function useI18n() {
  const t = useTranslations()
  const locale = useLocale() as SupportedLocale
  const localeConfig = SUPPORTED_LOCALES[locale]

  return {
    t,
    locale,
    localeConfig,
    switchLocale: (newLocale: SupportedLocale) => {
      if (typeof window !== 'undefined') {
        const currentPath = window.location.pathname
        // 移除当前语言前缀
        const pathWithoutLocale = currentPath.replace(/^\/[a-z-]+/, '') || '/'
        // 添加新语言前缀（如果不是默认语言）
        const newPath = newLocale === DEFAULT_LOCALE
          ? pathWithoutLocale
          : `/${newLocale}${pathWithoutLocale}`
        window.location.href = newPath
      }
    },
    formatCurrency: (amount: number, currency?: string) =>
      FormatUtils.formatCurrency(amount, locale, currency),
    formatNumber: (number: number, options?: Intl.NumberFormatOptions) =>
      FormatUtils.formatNumber(number, locale, options),
    formatDate: (date: Date | string, options?: Intl.DateTimeFormatOptions) =>
      FormatUtils.formatDate(date, locale, options),
    formatRelativeTime: (date: Date | string) =>
      FormatUtils.formatRelativeTime(date, locale)
  }
}

// 服务端翻译工具（重新导出 next-intl 的函数）
export { useTranslations, useLocale } from 'next-intl'

// 语言切换组件数据
export function getLanguageSwitcherData(currentLocale: SupportedLocale) {
  return Object.entries(SUPPORTED_LOCALES).map(([code, config]) => ({
    code: code as SupportedLocale,
    name: config.name,
    nativeName: config.nativeName,
    flag: config.flag,
    active: code === currentLocale
  }))
}

// SEO相关的语言工具
export class SEOUtils {
  /**
   * 生成hreflang标签
   */
  static generateHrefLangTags(path: string) {
    return Object.keys(SUPPORTED_LOCALES).map(locale => ({
      hrefLang: locale === 'zh-CN' ? 'zh-Hans' : locale,
      href: I18nUtils.getLocalizedUrl(path, locale as SupportedLocale)
    }))
  }

  /**
   * 获取本地化的元数据
   */
  static getLocalizedMetadata(
    baseMetadata: {
      title: string
      description: string
      keywords?: string
    },
    locale: SupportedLocale
  ) {
    const config = SUPPORTED_LOCALES[locale]
    
    return {
      ...baseMetadata,
      language: locale,
      locale: locale,
      alternateLanguages: Object.keys(SUPPORTED_LOCALES).reduce((acc, loc) => {
        acc[loc] = I18nUtils.getLocalizedUrl('/', loc as SupportedLocale)
        return acc
      }, {} as Record<string, string>)
    }
  }
}

// 导出常用类型和常量
export type LocaleConfig = typeof SUPPORTED_LOCALES[SupportedLocale]
export { SUPPORTED_LOCALES as LOCALES }
