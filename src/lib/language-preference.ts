/**
 * 语言偏好持久化工具
 * 用于保存和恢复用户的语言选择偏好
 */

import { type Locale } from '@/i18n'

const LANGUAGE_PREFERENCE_KEY = 'tucsenberg-language-preference'

export class LanguagePreference {
  /**
   * 保存用户的语言偏好到 localStorage
   */
  static save(locale: Locale): void {
    if (typeof window === 'undefined') return
    
    try {
      const preference = {
        locale,
        timestamp: Date.now(),
        version: '1.0'
      }
      localStorage.setItem(LANGUAGE_PREFERENCE_KEY, JSON.stringify(preference))
    } catch (error) {
      console.warn('Failed to save language preference:', error)
    }
  }

  /**
   * 从 localStorage 读取用户的语言偏好
   */
  static load(): Locale | null {
    if (typeof window === 'undefined') return null
    
    try {
      const stored = localStorage.getItem(LANGUAGE_PREFERENCE_KEY)
      if (!stored) return null

      const preference = JSON.parse(stored)
      
      // 验证数据格式和时效性（30天）
      if (
        preference &&
        preference.locale &&
        preference.timestamp &&
        Date.now() - preference.timestamp < 30 * 24 * 60 * 60 * 1000
      ) {
        return preference.locale as Locale
      }
    } catch (error) {
      console.warn('Failed to load language preference:', error)
    }
    
    return null
  }

  /**
   * 清除保存的语言偏好
   */
  static clear(): void {
    if (typeof window === 'undefined') return
    
    try {
      localStorage.removeItem(LANGUAGE_PREFERENCE_KEY)
    } catch (error) {
      console.warn('Failed to clear language preference:', error)
    }
  }

  /**
   * 检查是否有保存的语言偏好
   */
  static hasPreference(): boolean {
    return this.load() !== null
  }

  /**
   * 获取浏览器的首选语言
   */
  static getBrowserPreference(): Locale | null {
    if (typeof window === 'undefined') return null
    
    const browserLang = navigator.language || navigator.languages?.[0]
    if (!browserLang) return null

    // 映射浏览器语言到支持的语言
    const langMap: Record<string, Locale> = {
      'zh': 'zh-CN',
      'zh-CN': 'zh-CN',
      'zh-Hans': 'zh-CN',
      'zh-TW': 'zh-CN',
      'zh-Hant': 'zh-CN',
      'en': 'en',
      'en-US': 'en',
      'en-GB': 'en',
      'ja': 'ja',
      'ja-JP': 'ja',
      'es': 'es',
      'es-ES': 'es',
      'es-MX': 'es'
    }

    // 精确匹配
    if (langMap[browserLang]) {
      return langMap[browserLang]
    }

    // 语言前缀匹配
    const langPrefix = browserLang.split('-')[0]
    if (langMap[langPrefix]) {
      return langMap[langPrefix]
    }

    return null
  }

  /**
   * 获取推荐的语言（优先级：保存的偏好 > 浏览器偏好 > 默认语言）
   */
  static getRecommendedLocale(defaultLocale: Locale = 'en'): Locale {
    // 1. 检查保存的偏好
    const savedPreference = this.load()
    if (savedPreference) {
      return savedPreference
    }

    // 2. 检查浏览器偏好
    const browserPreference = this.getBrowserPreference()
    if (browserPreference) {
      return browserPreference
    }

    // 3. 返回默认语言
    return defaultLocale
  }
}

/**
 * React Hook 用于语言偏好管理
 */
export function useLanguagePreference() {
  const save = (locale: Locale) => {
    LanguagePreference.save(locale)
  }

  const load = () => {
    return LanguagePreference.load()
  }

  const clear = () => {
    LanguagePreference.clear()
  }

  const hasPreference = () => {
    return LanguagePreference.hasPreference()
  }

  const getRecommended = (defaultLocale: Locale = 'en') => {
    return LanguagePreference.getRecommendedLocale(defaultLocale)
  }

  return {
    save,
    load,
    clear,
    hasPreference,
    getRecommended
  }
}
