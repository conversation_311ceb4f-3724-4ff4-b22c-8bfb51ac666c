'use client'

import { useEffect, useState } from 'react'
import { CheckCircle } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { LOCALE_CONFIG, type Locale } from '@/i18n'

interface LanguageSwitchFeedbackProps {
  locale: Locale
  onComplete?: () => void
}

export default function LanguageSwitchFeedback({ 
  locale, 
  onComplete 
}: LanguageSwitchFeedbackProps) {
  const t = useTranslations('navigation')
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false)
      onComplete?.()
    }, 3000)

    return () => clearTimeout(timer)
  }, [onComplete])

  if (!isVisible) return null

  const localeConfig = LOCALE_CONFIG[locale]

  return (
    <div className="language-switch-success">
      <div className="flex items-center space-x-2">
        <CheckCircle className="h-4 w-4" />
        <span className="text-sm font-medium">
          {t('switchedTo')} {localeConfig.flag} {localeConfig.nativeName}
        </span>
      </div>
    </div>
  )
}

/**
 * 语言切换反馈管理器
 */
export class LanguageSwitchFeedbackManager {
  private static instance: LanguageSwitchFeedbackManager | null = null
  private container: HTMLDivElement | null = null

  static getInstance(): LanguageSwitchFeedbackManager {
    if (!this.instance) {
      this.instance = new LanguageSwitchFeedbackManager()
    }
    return this.instance
  }

  private createContainer(): HTMLDivElement {
    if (this.container) {
      return this.container
    }

    this.container = document.createElement('div')
    this.container.id = 'language-switch-feedback-container'
    this.container.style.position = 'fixed'
    this.container.style.top = '1rem'
    this.container.style.right = '1rem'
    this.container.style.zIndex = '9999'
    this.container.style.pointerEvents = 'none'
    
    document.body.appendChild(this.container)
    return this.container
  }

  show(locale: Locale): void {
    if (typeof window === 'undefined') return

    const container = this.createContainer()
    const localeConfig = LOCALE_CONFIG[locale]
    
    // 创建反馈元素
    const feedback = document.createElement('div')
    feedback.className = 'language-switch-success'
    feedback.innerHTML = `
      <div class="flex items-center space-x-2">
        <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
        </svg>
        <span class="text-sm font-medium">
          Switched to ${localeConfig.flag} ${localeConfig.nativeName}
        </span>
      </div>
    `

    container.appendChild(feedback)

    // 3秒后自动移除
    setTimeout(() => {
      if (feedback.parentNode) {
        feedback.parentNode.removeChild(feedback)
      }
    }, 3000)
  }

  clear(): void {
    if (this.container) {
      this.container.innerHTML = ''
    }
  }
}
