import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { sendContactEmail } from '@/lib/email'
import type { ContactFormData, ContactEmailData, ContactSubmissionResponse } from '@/types/contact'

// 请求验证模式
const contactRequestSchema = z.object({
  name: z.string().min(2).max(100),
  email: z.string().email(),
  company: z.string().max(100).optional(),
  phone: z.string().max(20).optional(),
  subject: z.string().min(5).max(200),
  message: z.string().min(10).max(2000),
  productInterest: z.array(z.string()).optional(),
  preferredContact: z.enum(['email', 'phone']),
  urgency: z.enum(['low', 'medium', 'high']),
  consent: z.boolean().refine(val => val === true),
  locale: z.string().default('en')
})

// 速率限制存储（生产环境应使用 Redis 或数据库）
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

/**
 * 简单的速率限制检查
 */
function checkRateLimit(ip: string): boolean {
  const now = Date.now()
  const windowMs = 15 * 60 * 1000 // 15 分钟
  const maxRequests = 5 // 每15分钟最多5次请求

  const record = rateLimitMap.get(ip)
  
  if (!record || now > record.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + windowMs })
    return true
  }

  if (record.count >= maxRequests) {
    return false
  }

  record.count++
  return true
}

/**
 * 获取客户端 IP 地址
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')

  if (forwarded) {
    return forwarded.split(',')[0]?.trim() || 'unknown'
  }

  if (realIP) {
    return realIP
  }

  return 'unknown'
}

/**
 * 清理和验证输入数据
 */
function sanitizeInput(data: any): ContactFormData {
  return {
    name: data.name.trim(),
    email: data.email.trim().toLowerCase(),
    company: data.company?.trim() || undefined,
    phone: data.phone?.trim() || undefined,
    subject: data.subject.trim(),
    message: data.message.trim(),
    productInterest: data.productInterest || [],
    preferredContact: data.preferredContact,
    urgency: data.urgency,
    consent: data.consent
  }
}

/**
 * POST /api/contact
 * 处理联系表单提交
 */
export async function POST(request: NextRequest): Promise<NextResponse<ContactSubmissionResponse>> {
  try {
    // 获取客户端信息
    const clientIP = getClientIP(request)
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // 速率限制检查
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        {
          success: false,
          message: 'Too many requests. Please try again later.',
          error: 'RATE_LIMIT_EXCEEDED'
        },
        { status: 429 }
      )
    }

    // 解析请求体
    let body
    try {
      body = await request.json()
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid JSON in request body',
          error: 'INVALID_JSON'
        },
        { status: 400 }
      )
    }

    // 验证输入数据
    let validatedData
    try {
      validatedData = contactRequestSchema.parse(body)
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            message: 'Validation failed',
            error: error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
          },
          { status: 400 }
        )
      }
      throw error
    }

    // 清理输入数据
    const sanitizedData = sanitizeInput(validatedData)

    // 额外验证：如果选择电话联系，必须提供电话号码
    if (sanitizedData.preferredContact === 'phone' && !sanitizedData.phone) {
      return NextResponse.json(
        {
          success: false,
          message: 'Phone number is required when phone is the preferred contact method',
          error: 'PHONE_REQUIRED'
        },
        { status: 400 }
      )
    }

    // 准备邮件数据
    const emailData: ContactEmailData = {
      ...sanitizedData,
      submittedAt: new Date().toISOString(),
      userAgent,
      ipAddress: clientIP,
      locale: validatedData.locale
    }

    // 发送邮件
    const emailResult = await sendContactEmail(emailData)

    if (!emailResult.success) {
      console.error('Failed to send contact email:', emailResult.error)
      return NextResponse.json(
        {
          success: false,
          message: 'Failed to send email. Please try again later.',
          error: 'EMAIL_SEND_FAILED'
        },
        { status: 500 }
      )
    }

    // 成功响应
    return NextResponse.json(
      {
        success: true,
        message: 'Your message has been sent successfully. We will get back to you soon.',
        id: emailResult.id
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('Contact form API error:', error)
    
    return NextResponse.json(
      {
        success: false,
        message: 'An unexpected error occurred. Please try again later.',
        error: 'INTERNAL_SERVER_ERROR'
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/contact
 * 返回联系表单配置信息（可选）
 */
export async function GET(): Promise<NextResponse> {
  return NextResponse.json({
    message: 'Contact form API is working',
    rateLimit: {
      windowMs: 15 * 60 * 1000,
      maxRequests: 5
    },
    supportedLocales: ['en', 'zh-CN', 'ja', 'es'],
    requiredFields: ['name', 'email', 'subject', 'message', 'preferredContact', 'urgency', 'consent']
  })
}

/**
 * OPTIONS /api/contact
 * CORS 预检请求处理
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
