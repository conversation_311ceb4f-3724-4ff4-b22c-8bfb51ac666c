'use client'

import { useEffect } from 'react'
import Link from 'next/link'
import { AlertTriangle, RefreshCw, Home, Mail } from 'lucide-react'

interface ErrorProps {
  error: Error & { digest?: string }
  reset: () => void
}

export default function Error({ error, reset }: ErrorProps) {
  useEffect(() => {
    // 记录错误到控制台（生产环境中应该发送到错误监控服务）
    console.error('Application error:', error)
    
    // 可以在这里集成 Sentry 或其他错误监控服务
    // Sentry.captureException(error)
  }, [error])

  const isDevelopment = process.env.NODE_ENV === 'development'

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* 错误图标 */}
        <div className="mb-8">
          <div className="inline-flex items-center justify-center w-24 h-24 bg-red-100 rounded-full mb-6">
            <AlertTriangle className="h-12 w-12 text-red-600" />
          </div>
          <div className="w-24 h-1 bg-red-600 mx-auto rounded-full"></div>
        </div>

        {/* 错误信息 */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Something went wrong
          </h1>
          <p className="text-lg text-gray-600 mb-6">
            We&apos;re sorry, but something unexpected happened.
            Our team has been notified and is working to fix the issue.
          </p>
        </div>

        {/* 开发环境错误详情 */}
        {isDevelopment && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8 text-left">
            <h2 className="text-lg font-semibold text-red-800 mb-3">
              Development Error Details
            </h2>
            <div className="space-y-2">
              <div>
                <span className="font-medium text-red-700">Error:</span>
                <p className="text-red-600 font-mono text-sm mt-1">
                  {error.message}
                </p>
              </div>
              {error.digest && (
                <div>
                  <span className="font-medium text-red-700">Digest:</span>
                  <p className="text-red-600 font-mono text-sm mt-1">
                    {error.digest}
                  </p>
                </div>
              )}
              {error.stack && (
                <div>
                  <span className="font-medium text-red-700">Stack Trace:</span>
                  <pre className="text-red-600 font-mono text-xs mt-1 overflow-x-auto whitespace-pre-wrap">
                    {error.stack}
                  </pre>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 操作建议 */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            What can you do?
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
            <div className="flex items-start space-x-3">
              <RefreshCw className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-gray-900">Try again</h3>
                <p className="text-sm text-gray-600">
                  The issue might be temporary
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Home className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-gray-900">Go to homepage</h3>
                <p className="text-sm text-gray-600">
                  Start fresh from our main page
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Mail className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-gray-900">Report the issue</h3>
                <p className="text-sm text-gray-600">
                  Help us improve by reporting this error
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-gray-900">Check status</h3>
                <p className="text-sm text-gray-600">
                  Visit our status page for updates
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="space-y-4 mb-8">
          <div className="flex flex-wrap justify-center gap-3">
            <button
              onClick={reset}
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </button>
            <Link
              href="/"
              className="inline-flex items-center px-6 py-3 bg-white text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
            >
              <Home className="h-4 w-4 mr-2" />
              Go Home
            </Link>
            <Link
              href="/contact"
              className="inline-flex items-center px-6 py-3 bg-white text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Mail className="h-4 w-4 mr-2" />
              Contact Support
            </Link>
          </div>
        </div>

        {/* 错误 ID（用于支持） */}
        {error.digest && (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-8">
            <h3 className="text-sm font-semibold text-gray-700 mb-2">
              Error Reference
            </h3>
            <p className="text-xs text-gray-600 font-mono">
              Error ID: {error.digest}
            </p>
            <p className="text-xs text-gray-500 mt-1">
              Please include this ID when contacting support
            </p>
          </div>
        )}

        {/* 联系信息 */}
        <div className="pt-8 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            Need immediate assistance? Contact us at{' '}
            <a 
              href="mailto:<EMAIL>" 
              className="text-blue-600 hover:text-blue-800"
            >
              <EMAIL>
            </a>
            {' '}or call our emergency hotline{' '}
            <a 
              href="tel:******-FLOOD-PROTECTION" 
              className="text-blue-600 hover:text-blue-800"
            >
              ******-FLOOD-PROTECTION
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
