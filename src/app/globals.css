@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义基础样式 */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-white text-neutral-900 antialiased;
  }
  
  /* 标题样式 */
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-semibold tracking-tight;
  }
  
  h1 {
    @apply text-4xl lg:text-5xl;
  }
  
  h2 {
    @apply text-3xl lg:text-4xl;
  }
  
  h3 {
    @apply text-2xl lg:text-3xl;
  }
  
  h4 {
    @apply text-xl lg:text-2xl;
  }
  
  /* 链接样式 */
  a {
    @apply transition-colors duration-200;
  }
  
  /* 按钮基础样式 */
  button {
    @apply transition-all duration-200;
  }
  
  /* 输入框样式 */
  input, textarea, select {
    @apply transition-colors duration-200;
  }
  
  /* 滚动条样式 */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-neutral-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-neutral-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-neutral-400;
  }
}

/* 自定义组件样式 */
@layer components {
  /* 按钮组件 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-outline {
    @apply btn border border-neutral-300 text-neutral-700 hover:bg-neutral-50 focus:ring-neutral-500;
  }
  
  /* 卡片组件 */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-neutral-200 overflow-hidden;
  }
  
  .card-hover {
    @apply card transition-all duration-300 hover:shadow-medium hover:-translate-y-1;
  }
  
  /* 容器样式 */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  /* 渐变背景 */
  .gradient-bg {
    @apply bg-gradient-to-br from-primary-50 via-white to-secondary-50;
  }
  
  /* 文本渐变 */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }
  
  /* 分割线 */
  .divider {
    @apply border-t border-neutral-200;
  }
  
  /* 响应式文本 */
  .text-responsive {
    @apply text-base sm:text-lg lg:text-xl;
  }

  /* 语言切换器动画 */
  .language-switcher-dropdown {
    @apply transform transition-all duration-200 ease-out;
    transform-origin: top right;
  }

  .language-switcher-dropdown.entering {
    @apply opacity-0 scale-95 -translate-y-1;
  }

  .language-switcher-dropdown.entered {
    @apply opacity-100 scale-100 translate-y-0;
  }

  .language-switcher-dropdown.exiting {
    @apply opacity-0 scale-95 -translate-y-1;
  }

  .language-switcher-item {
    @apply transform transition-all duration-150 ease-out;
  }

  .language-switcher-item:hover {
    @apply scale-[1.02] bg-opacity-80;
  }

  .language-switcher-item.active {
    @apply bg-primary-50 text-primary-700 scale-[1.01];
  }

  .language-switcher-button {
    @apply transform transition-all duration-200 ease-out;
  }

  .language-switcher-button:hover {
    @apply scale-105 shadow-md;
  }

  .language-switcher-button:active {
    @apply scale-95;
  }

  .language-switcher-button.loading {
    @apply opacity-75 cursor-not-allowed;
  }

  /* 语言切换成功反馈动画 */
  .language-switch-success {
    @apply fixed top-4 right-4 z-50 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg;
    animation: slideInRight 0.3s ease-out, fadeOut 0.3s ease-in 2.7s forwards;
  }

  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes fadeOut {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
      transform: translateX(100%);
    }
  }

  /* 加载旋转动画优化 */
  .loading-spinner {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}

/* 工具类 */
@layer utilities {
  /* 文本省略 */
  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* 居中布局 */
  .center-absolute {
    @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
  }
  
  /* 全屏覆盖 */
  .overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 z-50;
  }
  
  /* 安全区域 */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* MDX 内容样式 */
.prose {
  @apply max-w-none;
}

.prose h1 {
  @apply text-3xl font-bold text-neutral-900 mb-6;
}

.prose h2 {
  @apply text-2xl font-semibold text-neutral-800 mb-4 mt-8;
}

.prose h3 {
  @apply text-xl font-medium text-neutral-700 mb-3 mt-6;
}

.prose p {
  @apply text-neutral-600 leading-relaxed mb-4;
}

.prose ul, .prose ol {
  @apply text-neutral-600 mb-4;
}

.prose li {
  @apply mb-2;
}

.prose a {
  @apply text-primary-600 hover:text-primary-700 underline;
}

.prose blockquote {
  @apply border-l-4 border-primary-200 pl-4 italic text-neutral-700 my-6;
}

.prose code {
  @apply bg-neutral-100 text-neutral-800 px-2 py-1 rounded text-sm;
}

.prose pre {
  @apply bg-neutral-900 text-neutral-100 p-4 rounded-lg overflow-x-auto my-6;
}

.prose img {
  @apply rounded-lg shadow-soft my-6;
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    @apply text-black bg-white;
  }
  
  a {
    @apply text-black no-underline;
  }
}
