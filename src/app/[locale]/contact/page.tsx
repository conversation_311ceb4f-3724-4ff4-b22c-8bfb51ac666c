import { Metadata } from 'next'
import ContactForm from '@/components/forms/ContactForm'
import { Mail, Phone, MapPin, Clock } from 'lucide-react'

interface ContactPageProps {
  params: {
    locale: string
  }
}

export async function generateMetadata({ params }: ContactPageProps): Promise<Metadata> {
  return {
    title: 'Contact Us - Tucsenberg',
    description: 'Get in touch with <PERSON><PERSON><PERSON> for flood protection solutions. Contact our expert team for inquiries about flood barriers, water pumps, and emergency equipment.',
    keywords: 'contact, flood protection, emergency equipment, consultation, Tucsenberg',
    openGraph: {
      title: 'Contact Tucsenberg - Flood Protection Experts',
      description: 'Contact our team for professional flood protection solutions and emergency equipment.',
      type: 'website',
    }
  }
}

export default function ContactPage({ params }: ContactPageProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Contact Our Experts
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 mb-8">
              Ready to protect your property from flooding? Get in touch with our team for professional consultation and solutions.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Content */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              
              {/* Contact Information */}
              <div className="space-y-8">
                <div>
                  <h2 className="text-3xl font-bold text-gray-900 mb-6">
                    Get in Touch
                  </h2>
                  <p className="text-lg text-gray-600 mb-8">
                    Our team of flood protection experts is ready to help you find the right solution for your needs. Whether you&apos;re dealing with an emergency or planning ahead, we&apos;re here to assist.
                  </p>
                </div>

                {/* Contact Methods */}
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <Phone className="h-6 w-6 text-blue-600 mt-1" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Phone</h3>
                      <p className="text-gray-600">+****************</p>
                      <p className="text-sm text-gray-500">24/7 Emergency Hotline</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <Mail className="h-6 w-6 text-blue-600 mt-1" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Email</h3>
                      <p className="text-gray-600"><EMAIL></p>
                      <p className="text-sm text-gray-500">We respond within 24 hours</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <MapPin className="h-6 w-6 text-blue-600 mt-1" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Address</h3>
                      <p className="text-gray-600">
                        123 Flood Protection Ave<br />
                        Safety City, SC 12345<br />
                        United States
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <Clock className="h-6 w-6 text-blue-600 mt-1" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Business Hours</h3>
                      <div className="text-gray-600">
                        <p>Monday - Friday: 8:00 AM - 6:00 PM</p>
                        <p>Saturday: 9:00 AM - 4:00 PM</p>
                        <p>Sunday: Emergency calls only</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Emergency Notice */}
                <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-red-800 mb-2">
                    🚨 Emergency Situations
                  </h3>
                  <p className="text-red-700">
                    If you&apos;re experiencing active flooding or an emergency situation,
                    please call our 24/7 emergency hotline immediately at{' '}
                    <strong>+1 (555) 911-FLOOD</strong>
                  </p>
                </div>

                {/* Service Areas */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-blue-800 mb-3">
                    Service Areas
                  </h3>
                  <div className="text-blue-700">
                    <p className="mb-2">We provide services across:</p>
                    <ul className="list-disc list-inside space-y-1">
                      <li>North America (US & Canada)</li>
                      <li>Europe (EU countries)</li>
                      <li>Asia-Pacific region</li>
                      <li>Emergency response worldwide</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Contact Form */}
              <div className="bg-white rounded-lg shadow-lg p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">
                  Send us a Message
                </h2>
                <ContactForm 
                  onSuccess={() => {
                    // 可以添加成功后的处理逻辑
                    console.log('Contact form submitted successfully')
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
              Frequently Asked Questions
            </h2>
            
            <div className="space-y-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  How quickly can you respond to emergency situations?
                </h3>
                <p className="text-gray-600">
                  Our emergency response team is available 24/7 and can typically be on-site within 2-4 hours for critical situations, depending on your location.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Do you provide international services?
                </h3>
                <p className="text-gray-600">
                  Yes, we provide flood protection solutions worldwide. Our international team can assist with both emergency response and planned installations.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  What information should I include in my inquiry?
                </h3>
                <p className="text-gray-600">
                  Please include details about your location, the type of flooding risk, timeline requirements, and any specific products or services you&apos;re interested in.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Do you offer free consultations?
                </h3>
                <p className="text-gray-600">
                  Yes, we provide free initial consultations to assess your flood protection needs and recommend appropriate solutions.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

// App Router 中不需要 getStaticProps 和 getStaticPaths
// 多语言路由通过文件夹结构 [locale] 自动处理
