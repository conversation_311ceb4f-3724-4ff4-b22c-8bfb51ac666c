import Link from 'next/link'
import { Home, Search, ArrowLeft, Mail } from 'lucide-react'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        {/* 404 图标 */}
        <div className="mb-8">
          <div className="text-8xl md:text-9xl font-bold text-blue-600 mb-4">
            404
          </div>
          <div className="w-24 h-1 bg-blue-600 mx-auto rounded-full"></div>
        </div>

        {/* 错误信息 */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Page Not Found
          </h1>
          <p className="text-lg text-gray-600 mb-6">
            Sorry, we couldn&apos;t find the page you&apos;re looking for.
            The page might have been moved, deleted, or you entered the wrong URL.
          </p>
        </div>

        {/* 搜索建议 */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            What can you do?
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
            <div className="flex items-start space-x-3">
              <Search className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-gray-900">Search our site</h3>
                <p className="text-sm text-gray-600">
                  Use our search to find what you&apos;re looking for
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Home className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-gray-900">Go to homepage</h3>
                <p className="text-sm text-gray-600">
                  Start fresh from our main page
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <ArrowLeft className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-gray-900">Go back</h3>
                <p className="text-sm text-gray-600">
                  Return to the previous page
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Mail className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-gray-900">Contact us</h3>
                <p className="text-sm text-gray-600">
                  Let us know if you need help
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 快速链接 */}
        <div className="space-y-4 mb-8">
          <h2 className="text-xl font-semibold text-gray-900">
            Popular Pages
          </h2>
          <div className="flex flex-wrap justify-center gap-3">
            <Link
              href="/"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Home className="h-4 w-4 mr-2" />
              Home
            </Link>
            <Link
              href="/products"
              className="inline-flex items-center px-4 py-2 bg-white text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
            >
              Products
            </Link>
            <Link
              href="/about"
              className="inline-flex items-center px-4 py-2 bg-white text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
            >
              About Us
            </Link>
            <Link
              href="/contact"
              className="inline-flex items-center px-4 py-2 bg-white text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
            >
              <Mail className="h-4 w-4 mr-2" />
              Contact
            </Link>
          </div>
        </div>

        {/* 搜索框 */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Search Our Site
          </h3>
          <div className="flex">
            <input
              type="text"
              placeholder="Search products, articles..."
              className="flex-1 px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <button className="px-6 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 transition-colors">
              <Search className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* 返回按钮 */}
        <div className="mt-8">
          <button
            onClick={() => window.history.back()}
            className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go back to previous page
          </button>
        </div>

        {/* 联系信息 */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            Still need help? Contact us at{' '}
            <a 
              href="mailto:<EMAIL>" 
              className="text-blue-600 hover:text-blue-800"
            >
              <EMAIL>
            </a>
            {' '}or call{' '}
            <a 
              href="tel:******-FLOOD-PROTECTION" 
              className="text-blue-600 hover:text-blue-800"
            >
              ******-FLOOD-PROTECTION
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
